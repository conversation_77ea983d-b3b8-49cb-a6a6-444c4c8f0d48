// services/subscriptionService.js
import { api } from '@/utils/api';

export const subscriptionService = {
  /**
   * Obtém os planos disponíveis
   */
  async getPlans() {
    try {
      const response = await api.get('/subscription/plans');
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar planos:', error);
      throw error;
    }
  },

  /**
   * Obtém informações da assinatura atual
   */
  async getSubscription() {
    try {
      const response = await api.get('/subscription/subscription');
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar assinatura:', error);
      throw error;
    }
  },

  /**
   * Cria uma sessão de checkout do Stripe
   * @param {string} billingCycle - 'monthly' ou 'yearly'
   */
  async createCheckoutSession(billingCycle = 'monthly') {
    try {
      const response = await api.post('/subscription/checkout', {
        billingCycle
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao criar sessão de checkout:', error);
      throw error;
    }
  },

  /**
   * Adiciona um módulo à assinatura
   * @param {string} moduleType - Tipo do módulo a ser adicionado
   */
  async addModule(moduleType) {
    try {
      const response = await api.post('/subscription/module/add', {
        moduleType
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao adicionar módulo:', error);
      throw error;
    }
  },

  /**
   * Remove um módulo da assinatura
   * @param {string} moduleType - Tipo do módulo a ser removido
   */
  async removeModule(moduleType) {
    try {
      const response = await api.post('/subscription/module/remove', {
        moduleType
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao remover módulo:', error);
      throw error;
    }
  },

  /**
   * Cancela a assinatura
   * @param {boolean} cancelAtPeriodEnd - Se deve cancelar no final do período ou imediatamente
   */
  async cancelSubscription(cancelAtPeriodEnd = true) {
    try {
      const response = await api.post('/subscription/cancel', {
        cancelAtPeriodEnd
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao cancelar assinatura:', error);
      throw error;
    }
  },

  /**
   * Reativa uma assinatura cancelada
   */
  async reactivateSubscription() {
    try {
      const response = await api.post('/subscription/reactivate');
      return response.data;
    } catch (error) {
      console.error('Erro ao reativar assinatura:', error);
      throw error;
    }
  },

  /**
   * Atualiza o método de pagamento
   */
  async updatePaymentMethod() {
    try {
      const response = await api.post('/subscription/update-payment-method');
      return response.data;
    } catch (error) {
      console.error('Erro ao atualizar método de pagamento:', error);
      throw error;
    }
  },

  /**
   * Baixa uma fatura em PDF
   * @param {string} invoiceId - ID da fatura
   */
  async downloadInvoice(invoiceId) {
    try {
      const response = await api.get(`/subscription/invoices/${invoiceId}/download`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao baixar fatura:', error);
      throw error;
    }
  },

  /**
   * Obtém as faturas da assinatura
   */
  async getInvoices() {
    try {
      const response = await api.get('/subscription/invoices');
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar faturas:', error);
      throw error;
    }
  },

  /**
   * Adiciona usuários à assinatura
   * @param {number} additionalUsers - Quantidade de usuários a adicionar
   */
  async addUsers(additionalUsers) {
    try {
      const response = await api.post('/subscription/users/add', {
        additionalUsers
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao adicionar usuários:', error);
      throw error;
    }
  },

  /**
   * Faz upgrade do plano
   * @param {string} planType - Tipo do plano (professional, enterprise)
   * @param {number} userLimit - Novo limite de usuários
   */
  async upgradePlan(planType, userLimit) {
    try {
      const response = await api.post('/subscription/upgrade', {
        planType,
        userLimit
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao fazer upgrade do plano:', error);
      throw error;
    }
  }
};
