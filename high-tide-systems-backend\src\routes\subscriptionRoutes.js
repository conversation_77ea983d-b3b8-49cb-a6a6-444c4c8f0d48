// src/routes/subscriptionRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate: authMiddleware } = require('../middlewares/auth'); // Fixed import
const SubscriptionController = require('../controllers/subscriptionController');

// Rota para webhook do Stripe (sem autenticação)
router.post('/webhook', express.raw({ type: 'application/json' }), SubscriptionController.webhook);

// Todas as outras rotas requerem autenticação
router.use(authMiddleware);

// Rotas públicas (autenticadas)
router.get('/plans', SubscriptionController.getPlans);
router.get('/subscription', SubscriptionController.getSubscription);
router.get('/invoices', SubscriptionController.getInvoices);

// Rotas para gerenciar assinatura
router.post('/checkout', SubscriptionController.createCheckoutSession);
router.post('/module/add', SubscriptionController.addModule);
router.post('/module/remove', SubscriptionController.removeModule);
router.post('/cancel', SubscriptionController.cancelSubscription);
router.post('/reactivate', SubscriptionController.reactivateSubscription);
router.post('/update-payment-method', SubscriptionController.updatePaymentMethod);
router.post('/users/add', SubscriptionController.addUsers);
router.post('/upgrade', SubscriptionController.upgradePlan);
router.get('/invoices/:id/download', SubscriptionController.downloadInvoice);

module.exports = router;