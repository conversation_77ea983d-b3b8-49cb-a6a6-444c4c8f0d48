// src/controllers/subscriptionController.js
const stripeService = require('../services/stripeService');
const { MODULES, BASIC_INCLUDED_MODULES } = require('../config/stripe');
const { SystemModule } = require('@prisma/client'); // Added import
const prisma = require('../utils/prisma');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

class SubscriptionController {
  /**
   * Helper para obter ID da empresa com validação
   */
  static getCompanyIdFromRequest(req, allowSystemAdminWithoutCompany = false) {
    let companyId = req.body.companyId || req.query.companyId;

    if (req.user.role !== 'SYSTEM_ADMIN') {
      companyId = req.user.companyId;
      if (!companyId) {
        throw new Error('Usuário não está associado a nenhuma empresa');
      }
    } else {
      // Para SYSTEM_ADMIN, se não foi fornecido companyId e é permitido, retorna null
      if (!companyId && allowSystemAdminWithoutCompany) {
        return null;
      } else if (!companyId && !allowSystemAdminWithoutCompany) {
        throw new Error('ID da empresa não fornecido para administrador do sistema');
      }
    }
    return companyId;
  }

  /**
   * Obtém os planos disponíveis
   */
  static async getPlans(req, res) {
    try {
      res.json({
        modules: MODULES,
        basicModules: BASIC_INCLUDED_MODULES,
      });
    } catch (error) {
      console.error('Erro ao obter planos:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Cria uma sessão de checkout para assinatura
   */
  static async createCheckoutSession(req, res) {
    try {
      const { billingCycle = 'monthly' } = req.body;

      // Verifica se o ciclo de faturamento é válido
      if (!['monthly', 'yearly'].includes(billingCycle)) {
        return res.status(400).json({ message: 'Ciclo de faturamento inválido' });
      }

      // Obtém o ID da empresa a partir do request
      let companyId;
      try {
        companyId = SubscriptionController.getCompanyIdFromRequest(req);
      } catch (error) {
        return res.status(400).json({ message: error.message });
      }

      // Cria a sessão de checkout
      const session = await stripeService.createCheckoutSession(
        companyId,
        billingCycle
      );

      res.json({
        sessionId: session.id,
        url: session.url
      });
    } catch (error) {
      console.error('Erro ao criar sessão de checkout:', error);
      res.status(500).json({ message: error.message || 'Erro interno do servidor' });
    }
  }

  /**
   * Obtém detalhes da assinatura atual
   */
  static async getSubscription(req, res) {
    try {
      // Obtém o ID da empresa a partir do request
      let companyId;
      try {
        companyId = SubscriptionController.getCompanyIdFromRequest(req, true);
      } catch (error) {
        return res.status(400).json({ message: error.message });
      }

      // Se for SYSTEM_ADMIN sem empresa específica, retorna null
      if (!companyId && req.user.role === 'SYSTEM_ADMIN') {
        return res.json(null);
      }

      // Busca a assinatura
      const subscription = await prisma.subscription.findUnique({
        where: { companyId },
        include: {
          modules: true,
          invoices: {
            orderBy: { createdAt: 'desc' },
            take: 10, // Limita a 10 faturas recentes
          },
        },
      });

      if (!subscription) {
        return res.status(404).json({ message: 'Assinatura não encontrada' });
      }

      // Adiciona informações dos produtos para cada módulo
      const modulesWithDetails = subscription.modules.map(module => ({
        ...module,
        details: MODULES[module.moduleType] || {
          name: module.moduleType,
          description: 'Módulo do sistema',
        },
      }));

      res.json({
        ...subscription,
        modules: modulesWithDetails,
      });
    } catch (error) {
      console.error('Erro ao obter assinatura:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Adiciona um módulo à assinatura
   */
  static async addModule(req, res) {
    try {
      const { moduleType } = req.body;
      
      // Verifica se o módulo é válido
      if (!MODULES[moduleType]) {
        return res.status(400).json({ message: 'Módulo inválido' });
      }

      // Obtém o ID da empresa a partir do request
      let companyId;
      try {
        companyId = SubscriptionController.getCompanyIdFromRequest(req);
      } catch (error) {
        return res.status(400).json({ message: error.message });
      }

      // Busca informações da assinatura para log
      const company = await prisma.company.findUnique({
        where: { id: companyId },
        select: { subscription: { select: { id: true } } }
      });

      const result = await stripeService.addModuleToSubscription(
        companyId,
        moduleType
      );

      // Adiciona o id do usuário ao log de auditoria
      if (company && company.subscription) {
        await prisma.auditLog.create({
          data: {
            userId: req.user.id,
            action: 'UPDATE',
            entityType: 'Subscription',
            entityId: company.subscription.id, // Agora temos o ID correto
            details: {
              action: 'add_module',
              moduleType: moduleType,
            },
            companyId: companyId,
          },
        });
      }

      res.json(result);
    } catch (error) {
      console.error('Erro ao adicionar módulo:', error);
      res.status(500).json({ message: error.message || 'Erro interno do servidor' });
    }
  }

  /**
   * Remove um módulo da assinatura
   */
  static async removeModule(req, res) {
    try {
      const { moduleType } = req.body;
      
      // Verifica se o módulo é válido
      if (!MODULES[moduleType]) {
        return res.status(400).json({ message: 'Módulo inválido' });
      }

      // Verifica se é um módulo que não pode ser removido
      if ([SystemModule.BASIC, SystemModule.ADMIN, SystemModule.SCHEDULING].includes(moduleType)) {
        return res.status(400).json({ 
          message: 'Este módulo não pode ser removido do plano básico' 
        });
      }

      // Obtém o ID da empresa a partir do request
      let companyId;
      try {
        companyId = SubscriptionController.getCompanyIdFromRequest(req);
      } catch (error) {
        return res.status(400).json({ message: error.message });
      }

      // Busca informações da assinatura para log
      const company = await prisma.company.findUnique({
        where: { id: companyId },
        select: { subscription: { select: { id: true } } }
      });

      const result = await stripeService.removeModuleFromSubscription(
        companyId,
        moduleType
      );

      // Adiciona o id do usuário ao log de auditoria
      if (company && company.subscription) {
        await prisma.auditLog.create({
          data: {
            userId: req.user.id,
            action: 'UPDATE',
            entityType: 'Subscription',
            entityId: company.subscription.id, // Agora temos o ID correto
            details: {
              action: 'remove_module',
              moduleType: moduleType,
            },
            companyId: companyId,
          },
        });
      }

      res.json(result);
    } catch (error) {
      console.error('Erro ao remover módulo:', error);
      res.status(500).json({ message: error.message || 'Erro interno do servidor' });
    }
  }

  /**
   * Cancela uma assinatura
   */
  static async cancelSubscription(req, res) {
    try {
      const { cancelAtPeriodEnd = true } = req.body;

      // Obtém o ID da empresa a partir do request
      let companyId;
      try {
        companyId = SubscriptionController.getCompanyIdFromRequest(req);
      } catch (error) {
        return res.status(400).json({ message: error.message });
      }

      // Busca informações da assinatura para log
      const company = await prisma.company.findUnique({
        where: { id: companyId },
        select: { subscription: { select: { id: true } } }
      });

      const result = await stripeService.cancelSubscription(
        companyId,
        cancelAtPeriodEnd
      );

      // Adiciona o id do usuário ao log de auditoria
      if (company && company.subscription) {
        await prisma.auditLog.create({
          data: {
            userId: req.user.id,
            action: 'UPDATE',
            entityType: 'Subscription',
            entityId: company.subscription.id, // Agora temos o ID correto
            details: {
              action: 'cancel_subscription',
              cancelAtPeriodEnd,
            },
            companyId: companyId,
          },
        });
      }

      res.json(result);
    } catch (error) {
      console.error('Erro ao cancelar assinatura:', error);
      res.status(500).json({ message: error.message || 'Erro interno do servidor' });
    }
  }

  /**
   * Webhook do Stripe
   */
  static async webhook(req, res) {
    const signature = req.headers['stripe-signature'];
    
    if (!signature) {
      return res.status(400).json({ message: 'Assinatura Stripe não fornecida' });
    }

    try {
      const stripe = require('../config/stripe').stripe;
      
      // Verifica a assinatura do webhook
      const event = stripe.webhooks.constructEvent(
        req.body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET
      );

      // Processa o evento
      await stripeService.handleWebhook(event);

      res.json({ received: true });
    } catch (error) {
      console.error('Erro no webhook do Stripe:', error);
      res.status(400).json({ message: `Erro no webhook: ${error.message}` });
    }
  }

  /**
   * Obtém o histórico de faturas
   */
  static async getInvoices(req, res) {
    try {
      // Obtém o ID da empresa a partir do request
      let companyId;
      try {
        companyId = SubscriptionController.getCompanyIdFromRequest(req, true);
      } catch (error) {
        return res.status(400).json({ message: error.message });
      }

      // Se for SYSTEM_ADMIN sem empresa específica, retorna array vazio
      if (!companyId && req.user.role === 'SYSTEM_ADMIN') {
        return res.json({
          invoices: [],
          pagination: {
            total: 0,
            pages: 0,
            page: 1,
            limit: 10,
          },
        });
      }

      // Paginação
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // Busca as faturas
      const invoices = await prisma.invoice.findMany({
        where: { companyId },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      });

      // Conta o total de faturas
      const total = await prisma.invoice.count({
        where: { companyId },
      });

      res.json({
        invoices,
        pagination: {
          total,
          pages: Math.ceil(total / limit),
          page,
          limit,
        },
      });
    } catch (error) {
      console.error('Erro ao obter faturas:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Adiciona usuários à assinatura
   */
  static async addUsers(req, res) {
    try {
      const { additionalUsers } = req.body;

      if (!additionalUsers || additionalUsers <= 0) {
        return res.status(400).json({ message: 'Quantidade de usuários inválida' });
      }

      // Obtém o ID da empresa a partir do request
      let companyId;
      try {
        companyId = SubscriptionController.getCompanyIdFromRequest(req);
      } catch (error) {
        return res.status(400).json({ message: error.message });
      }

      // Busca a assinatura atual
      const subscription = await prisma.subscription.findUnique({
        where: { companyId },
      });

      if (!subscription) {
        return res.status(404).json({ message: 'Assinatura não encontrada' });
      }

      // Calcula o novo limite e preço
      const newUserLimit = subscription.userLimit + additionalUsers;
      const basePrice = 19.90;

      // Função para calcular desconto baseado na quantidade de usuários
      const getDiscountByUserCount = (users) => {
        if (users >= 200) return 40;
        if (users >= 100) return 35;
        if (users >= 50) return 25;
        if (users >= 20) return 15;
        if (users >= 5) return 10;
        return 0;
      };

      const discount = getDiscountByUserCount(newUserLimit);
      const totalWithoutDiscount = newUserLimit * basePrice;
      const discountAmount = totalWithoutDiscount * (discount / 100);
      const newPricePerMonth = totalWithoutDiscount - discountAmount;

      // Atualiza a assinatura
      const updatedSubscription = await prisma.subscription.update({
        where: { companyId },
        data: {
          userLimit: newUserLimit,
          pricePerMonth: newPricePerMonth,
        },
      });

      // Log de auditoria
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'UPDATE',
          entityType: 'Subscription',
          entityId: subscription.id,
          details: {
            action: 'add_users',
            additionalUsers,
            newUserLimit,
            newPricePerMonth,
          },
          companyId,
        },
      });

      res.json({
        success: true,
        message: `${additionalUsers} usuários adicionados com sucesso`,
        subscription: updatedSubscription,
      });
    } catch (error) {
      console.error('Erro ao adicionar usuários:', error);
      res.status(500).json({ message: error.message || 'Erro interno do servidor' });
    }
  }

  /**
   * Faz upgrade do plano
   */
  static async upgradePlan(req, res) {
    try {
      const { planType, userLimit } = req.body;

      if (!planType || !userLimit) {
        return res.status(400).json({ message: 'Tipo de plano e limite de usuários são obrigatórios' });
      }

      // Obtém o ID da empresa a partir do request
      let companyId;
      try {
        companyId = SubscriptionController.getCompanyIdFromRequest(req);
      } catch (error) {
        return res.status(400).json({ message: error.message });
      }

      // Busca a assinatura atual
      const subscription = await prisma.subscription.findUnique({
        where: { companyId },
      });

      if (!subscription) {
        return res.status(404).json({ message: 'Assinatura não encontrada' });
      }

      // Define preços baseados no tipo de plano
      let newPricePerMonth;
      switch (planType) {
        case 'professional':
          newPricePerMonth = 299.90;
          break;
        case 'enterprise':
          newPricePerMonth = 599.90;
          break;
        default:
          return res.status(400).json({ message: 'Tipo de plano inválido' });
      }

      // Atualiza a assinatura
      const updatedSubscription = await prisma.subscription.update({
        where: { companyId },
        data: {
          userLimit: userLimit,
          pricePerMonth: newPricePerMonth,
        },
      });

      // Log de auditoria
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'UPDATE',
          entityType: 'Subscription',
          entityId: subscription.id,
          details: {
            action: 'upgrade_plan',
            planType,
            newUserLimit: userLimit,
            newPricePerMonth,
          },
          companyId,
        },
      });

      res.json({
        success: true,
        message: `Plano atualizado para ${planType} com sucesso`,
        subscription: updatedSubscription,
      });
    } catch (error) {
      console.error('Erro ao fazer upgrade do plano:', error);
      res.status(500).json({ message: error.message || 'Erro interno do servidor' });
    }
  }

  /**
   * Reativa uma assinatura cancelada
   */
  static async reactivateSubscription(req, res) {
    try {
      // Obtém o ID da empresa a partir do request
      let companyId;
      try {
        companyId = SubscriptionController.getCompanyIdFromRequest(req);
      } catch (error) {
        return res.status(400).json({ message: error.message });
      }

      // Busca informações da assinatura para log
      const company = await prisma.company.findUnique({
        where: { id: companyId },
        select: { subscription: { select: { id: true, stripeSubscriptionId: true } } }
      });

      if (!company?.subscription) {
        return res.status(404).json({ message: 'Assinatura não encontrada' });
      }

      // Reativa a assinatura no Stripe
      await stripe.subscriptions.update(company.subscription.stripeSubscriptionId, {
        cancel_at_period_end: false,
      });

      // Atualiza o status da assinatura no banco de dados
      await prisma.subscription.update({
        where: { id: company.subscription.id },
        data: {
          cancelAtPeriodEnd: false,
          status: 'ACTIVE',
          active: true,
          endDate: null,
        },
      });

      // Log de auditoria
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'UPDATE',
          entityType: 'Subscription',
          entityId: company.subscription.id,
          details: {
            action: 'reactivate_subscription',
          },
          companyId: companyId,
        },
      });

      res.json({
        success: true,
        message: 'Assinatura reativada com sucesso',
      });
    } catch (error) {
      console.error('Erro ao reativar assinatura:', error);
      res.status(500).json({ message: error.message || 'Erro interno do servidor' });
    }
  }

  /**
   * Atualiza o método de pagamento
   */
  static async updatePaymentMethod(req, res) {
    try {
      // Obtém o ID da empresa a partir do request
      let companyId;
      try {
        companyId = SubscriptionController.getCompanyIdFromRequest(req);
      } catch (error) {
        return res.status(400).json({ message: error.message });
      }

      // Busca informações da assinatura
      const company = await prisma.company.findUnique({
        where: { id: companyId },
        select: { subscription: { select: { stripeCustomerId: true } } }
      });

      if (!company?.subscription?.stripeCustomerId) {
        return res.status(404).json({ message: 'Cliente Stripe não encontrado' });
      }

      // Cria uma sessão do portal de cobrança do Stripe
      const session = await stripe.billingPortal.sessions.create({
        customer: company.subscription.stripeCustomerId,
        return_url: `${process.env.FRONTEND_URL}/dashboard/admin/plans`,
      });

      res.json({
        url: session.url
      });
    } catch (error) {
      console.error('Erro ao atualizar método de pagamento:', error);
      res.status(500).json({ message: error.message || 'Erro interno do servidor' });
    }
  }

  /**
   * Baixa uma fatura em PDF
   */
  static async downloadInvoice(req, res) {
    try {
      const { id: invoiceId } = req.params;

      // Obtém o ID da empresa a partir do request
      let companyId;
      try {
        companyId = SubscriptionController.getCompanyIdFromRequest(req);
      } catch (error) {
        return res.status(400).json({ message: error.message });
      }

      // Busca a fatura
      const invoice = await prisma.invoice.findFirst({
        where: {
          id: invoiceId,
          companyId: companyId
        }
      });

      if (!invoice) {
        return res.status(404).json({ message: 'Fatura não encontrada' });
      }

      if (!invoice.stripeInvoiceId) {
        return res.status(400).json({ message: 'Fatura não possui ID do Stripe' });
      }

      // Busca a fatura no Stripe
      const stripeInvoice = await stripe.invoices.retrieve(invoice.stripeInvoiceId);

      if (!stripeInvoice.invoice_pdf) {
        return res.status(400).json({ message: 'PDF da fatura não disponível' });
      }

      // Redireciona para o PDF da fatura
      res.redirect(stripeInvoice.invoice_pdf);
    } catch (error) {
      console.error('Erro ao baixar fatura:', error);
      res.status(500).json({ message: error.message || 'Erro interno do servidor' });
    }
  }
}

module.exports = SubscriptionController;