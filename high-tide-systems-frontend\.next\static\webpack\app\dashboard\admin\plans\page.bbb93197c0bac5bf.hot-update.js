"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js":
/*!**************************************************!*\
  !*** ./src/app/modules/admin/plans/PlansPage.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/subscriptionService */ \"(app-pages-browser)/./src/services/subscriptionService.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ModuleCard */ \"(app-pages-browser)/./src/components/ui/ModuleCard.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst PlansPage = ()=>{\n    var _subscription_modules;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { addToast } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [subscription, setSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [invoices, setInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showModuleModal, setShowModuleModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUpgradeModal, setShowUpgradeModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUserModal, setShowUserModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showReactivateModal, setShowReactivateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPaymentModal, setShowPaymentModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showInvoiceModal, setShowInvoiceModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUsageModal, setShowUsageModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedModule, setSelectedModule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCompanyId, setSelectedCompanyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userStats, setUserStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 0,\n        limit: 0\n    });\n    const [customUserCount, setCustomUserCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [cancelationType, setCancelationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('end_period'); // 'end_period' ou 'immediate'\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN') {\n                loadCompanies();\n            } else {\n                loadData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (selectedCompanyId) {\n                loadData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        selectedCompanyId\n    ]);\n    const loadCompanies = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.get('/companies?limit=100');\n            setCompanies(response.data.companies || []);\n        } catch (error) {\n            console.error('Erro ao carregar empresas:', error);\n            addToast('Erro ao carregar lista de empresas', 'error');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const loadData = async ()=>{\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN' && !selectedCompanyId) {\n            return;\n        }\n        try {\n            setIsLoading(true);\n            // Para SYSTEM_ADMIN, adiciona o companyId como query parameter\n            const queryParams = (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN' && selectedCompanyId ? \"?companyId=\".concat(selectedCompanyId) : '';\n            // Carregar dados com tratamento individual de erros\n            let subscriptionData = null;\n            let invoicesData = [];\n            let plansData = null;\n            let userStatsData = {\n                current: 0,\n                limit: 0\n            };\n            try {\n                subscriptionData = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.get(\"/subscription/subscription\".concat(queryParams)).then((res)=>res.data);\n            } catch (error) {\n                var _error_response;\n                if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) {\n                    // Empresa não tem assinatura - isso é normal\n                    subscriptionData = null;\n                } else {\n                    throw error; // Re-throw outros erros\n                }\n            }\n            try {\n                invoicesData = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.get(\"/subscription/invoices\".concat(queryParams)).then((res)=>res.data);\n            } catch (error) {\n                var _error_response1;\n                if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 404) {\n                    // Empresa não tem faturas - isso é normal\n                    invoicesData = {\n                        invoices: []\n                    };\n                } else {\n                    console.warn('Erro ao carregar faturas:', error);\n                    invoicesData = {\n                        invoices: []\n                    };\n                }\n            }\n            try {\n                plansData = await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.getPlans();\n            } catch (error) {\n                console.warn('Erro ao carregar planos:', error);\n                plansData = null;\n            }\n            // Carregar estatísticas de usuários\n            try {\n                var _usersResponse_data_users;\n                const usersResponse = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.get(\"/users\".concat(queryParams, \"&limit=1000\"));\n                const activeUsers = ((_usersResponse_data_users = usersResponse.data.users) === null || _usersResponse_data_users === void 0 ? void 0 : _usersResponse_data_users.filter((u)=>u.active)) || [];\n                userStatsData = {\n                    current: activeUsers.length,\n                    limit: (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.userLimit) || 50 // Usar o limite da subscription\n                };\n            } catch (error) {\n                console.warn('Erro ao carregar estatísticas de usuários:', error);\n                // Se houver erro, usar dados da subscription se disponível\n                if (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.userLimit) {\n                    userStatsData.limit = subscriptionData.userLimit;\n                }\n            }\n            setSubscription(subscriptionData);\n            setPlans(plansData);\n            setInvoices((invoicesData === null || invoicesData === void 0 ? void 0 : invoicesData.invoices) || []);\n            setUserStats(userStatsData);\n        } catch (error) {\n            console.error('Erro ao carregar dados:', error);\n            addToast('Erro ao carregar informações do plano', 'error');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddModule = async (moduleType)=>{\n        try {\n            setActionLoading(true);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.addModule(moduleType);\n            addToast('Módulo adicionado com sucesso!', 'success');\n            await loadData();\n            setShowModuleModal(false);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao adicionar módulo:', error);\n            addToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao adicionar módulo', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleRemoveModule = async (moduleType)=>{\n        try {\n            setActionLoading(true);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.removeModule(moduleType);\n            addToast('Módulo removido com sucesso!', 'success');\n            await loadData();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao remover módulo:', error);\n            addToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao remover módulo', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleCancelSubscription = async ()=>{\n        try {\n            setActionLoading(true);\n            const cancelAtPeriodEnd = cancelationType === 'end_period';\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.cancelSubscription(cancelAtPeriodEnd);\n            const message = cancelAtPeriodEnd ? 'Assinatura será cancelada no final do período atual!' : 'Assinatura cancelada imediatamente!';\n            addToast(message, 'success');\n            await loadData();\n            setShowCancelModal(false);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao cancelar assinatura:', error);\n            addToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao cancelar assinatura', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleReactivateSubscription = async ()=>{\n        try {\n            setActionLoading(true);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.reactivateSubscription();\n            addToast('Assinatura reativada com sucesso!', 'success');\n            await loadData();\n            setShowReactivateModal(false);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao reativar assinatura:', error);\n            addToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao reativar assinatura', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleUpdatePaymentMethod = async ()=>{\n        try {\n            setActionLoading(true);\n            const result = await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.updatePaymentMethod();\n            if (result.url) {\n                window.open(result.url, '_blank');\n                addToast('Redirecionando para atualização do método de pagamento...', 'info');\n            }\n            setShowPaymentModal(false);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao atualizar método de pagamento:', error);\n            addToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao atualizar método de pagamento', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleDownloadInvoice = async (invoiceId, invoiceNumber)=>{\n        try {\n            const blob = await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.downloadInvoice(invoiceId);\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"fatura-\".concat(invoiceNumber, \".pdf\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            addToast('Fatura baixada com sucesso!', 'success');\n        } catch (error) {\n            console.error('Erro ao baixar fatura:', error);\n            addToast('Erro ao baixar fatura', 'error');\n        }\n    };\n    const handleUpgradePlan = async function(planType) {\n        let userLimit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n        try {\n            setActionLoading(true);\n            // Se não foi especificado userLimit, usar um valor baseado no plano\n            const finalUserLimit = userLimit || (planType === 'professional' ? 999 : 9999);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.upgradePlan(planType, finalUserLimit);\n            addToast(\"Plano atualizado para \".concat(planType, \" com sucesso!\"), 'success');\n            await loadData();\n            setShowUpgradeModal(false);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao fazer upgrade:', error);\n            addToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao fazer upgrade', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleBuyMoreUsers = async (additionalUsers)=>{\n        try {\n            setActionLoading(true);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.addUsers(additionalUsers);\n            addToast(\"\".concat(additionalUsers, \" usu\\xe1rios adicionais adquiridos!\"), 'success');\n            await loadData();\n            setShowUserModal(false);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao comprar usuários:', error);\n            addToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao comprar usuários', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return 'text-green-600 bg-green-100';\n            case 'TRIAL':\n                return 'text-blue-600 bg-blue-100';\n            case 'PAST_DUE':\n                return 'text-yellow-600 bg-yellow-100';\n            case 'CANCELED':\n                return 'text-red-600 bg-red-100';\n            default:\n                return 'text-gray-600 bg-gray-100';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return 'Ativo';\n            case 'TRIAL':\n                return 'Período de Teste';\n            case 'PAST_DUE':\n                return 'Pagamento em Atraso';\n            case 'CANCELED':\n                return 'Cancelado';\n            default:\n                return status;\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('pt-BR');\n    };\n    const formatCurrency = (value)=>{\n        return new Intl.NumberFormat('pt-BR', {\n            style: 'currency',\n            currency: 'BRL'\n        }).format(value);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-admin-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 337,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 336,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Se for SYSTEM_ADMIN, mostrar seletor de empresa\n    if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN') {\n        var _subscription_modules1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    description: \"Selecione uma empresa para gerenciar sua assinatura, m\\xf3dulos e faturamento\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        size: 20\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 349,\n                        columnNumber: 17\n                    }, void 0),\n                    moduleColor: \"admin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 346,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    moduleColor: \"admin\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                                        children: \"Selecionar Empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleSelect, {\n                                        value: selectedCompanyId || '',\n                                        onChange: (e)=>setSelectedCompanyId(e.target.value),\n                                        moduleColor: \"admin\",\n                                        className: \"w-full max-w-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Selecione uma empresa...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: company.id,\n                                                    children: company.name\n                                                }, company.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, undefined),\n                            !selectedCompanyId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-auto w-12 h-12 bg-admin-primary/10 rounded-full flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-admin-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 377,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 376,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: \"Selecione uma empresa acima para visualizar e gerenciar seus planos de assinatura.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 379,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 375,\n                                columnNumber: 15\n                            }, undefined),\n                            selectedCompanyId && !subscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-auto w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-yellow-600 dark:text-yellow-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 388,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 387,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                        children: \"Nenhuma Assinatura Encontrada\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 390,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: \"A empresa selecionada n\\xe3o possui uma assinatura ativa no momento.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 393,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 386,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 353,\n                    columnNumber: 9\n                }, undefined),\n                selectedCompanyId && subscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                moduleColor: \"admin\",\n                                className: \"lg:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-admin-primary/10 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5 text-admin-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                                children: \"Plano da Empresa\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                children: subscription.billingCycle === 'YEARLY' ? 'Faturamento Anual' : 'Faturamento Mensal'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(getStatusColor(subscription.status)),\n                                                children: getStatusText(subscription.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 422,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                                                children: \"Valor Mensal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                                children: formatCurrency(subscription.pricePerMonth)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 431,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                                                children: \"Pr\\xf3ximo Pagamento\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                                children: subscription.nextBillingDate ? formatDate(subscription.nextBillingDate) : 'N/A'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 428,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                                            children: \"Data de In\\xedcio\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: formatDate(subscription.startDate)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 442,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 427,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    subscription.cancelAtPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-yellow-800 dark:text-yellow-200\",\n                                                            children: \"Cancelamento Agendado\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-yellow-700 dark:text-yellow-300\",\n                                                            children: \"A assinatura desta empresa ser\\xe1 cancelada no final do per\\xedodo atual.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 454,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 453,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 407,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                moduleColor: \"admin\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mx-auto w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 473,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 472,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                            children: \"Usu\\xe1rios\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 475,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-blue-600 dark:text-blue-400\",\n                                                    children: userStats.current\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: [\n                                                        \"de \",\n                                                        userStats.limit,\n                                                        \" dispon\\xedveis\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 478,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                                style: {\n                                                    width: \"\".concat(Math.min(userStats.current / userStats.limit * 100, 100), \"%\")\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 487,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 486,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 471,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 470,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                moduleColor: \"admin\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-6 w-6 text-green-600 dark:text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 499,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 498,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                            children: \"M\\xf3dulos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 501,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-green-600 dark:text-green-400 mb-4\",\n                                            children: ((_subscription_modules1 = subscription.modules) === null || _subscription_modules1 === void 0 ? void 0 : _subscription_modules1.filter((m)=>m.active).length) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 504,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setShowModuleModal(true),\n                                            moduleColor: \"admin\",\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Gerenciar\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 507,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 497,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 496,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 405,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 345,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleHeader, {\n                title: \"Gerenciamento de Planos\",\n                description: \"Gerencie sua assinatura, m\\xf3dulos e faturamento\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: 20\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 531,\n                    columnNumber: 15\n                }, void 0),\n                moduleColor: \"admin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 528,\n                columnNumber: 7\n            }, undefined),\n            subscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        moduleColor: \"admin\",\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-admin-primary/10 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-admin-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 542,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                        children: \"Plano Atual\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: subscription.billingCycle === 'YEARLY' ? 'Faturamento Anual' : 'Faturamento Mensal'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 545,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 541,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(getStatusColor(subscription.status)),\n                                        children: getStatusText(subscription.status)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 540,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                                        children: \"Valor Mensal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: formatCurrency(subscription.pricePerMonth)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                                        children: \"Pr\\xf3ximo Pagamento\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: subscription.nextBillingDate ? formatDate(subscription.nextBillingDate) : 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 567,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 560,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                                        children: \"Data de In\\xedcio\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: formatDate(subscription.startDate)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 575,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>setShowUpgradeModal(true),\n                                                    moduleColor: \"admin\",\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 589,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Fazer Upgrade\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 581,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 574,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 559,\n                                columnNumber: 13\n                            }, undefined),\n                            subscription.cancelAtPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 599,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-yellow-800 dark:text-yellow-200\",\n                                                    children: \"Cancelamento Agendado\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-yellow-700 dark:text-yellow-300\",\n                                                    children: \"Sua assinatura ser\\xe1 cancelada no final do per\\xedodo atual.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 600,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 598,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 597,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 539,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        moduleColor: \"admin\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 617,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 616,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: \"Usu\\xe1rios\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 619,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-blue-600 dark:text-blue-400\",\n                                            children: userStats.current\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 623,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                \"de \",\n                                                userStats.limit,\n                                                \" dispon\\xedveis\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 626,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 622,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                        style: {\n                                            width: \"\".concat(Math.min(userStats.current / userStats.limit * 100, 100), \"%\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 631,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 630,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setShowUserModal(true),\n                                    moduleColor: \"admin\",\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 643,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Comprar Usu\\xe1rios\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 636,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 615,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 614,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        moduleColor: \"admin\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-6 w-6 text-green-600 dark:text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 653,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 652,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: \"M\\xf3dulos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 655,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-3xl font-bold text-green-600 dark:text-green-400 mb-4\",\n                                    children: ((_subscription_modules = subscription.modules) === null || _subscription_modules === void 0 ? void 0 : _subscription_modules.filter((m)=>m.active).length) || 0\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 658,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setShowModuleModal(true),\n                                    moduleColor: \"admin\",\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 668,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Gerenciar\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 661,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 651,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 650,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 537,\n                columnNumber: 9\n            }, undefined),\n            (subscription === null || subscription === void 0 ? void 0 : subscription.modules) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                moduleColor: \"admin\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                children: \"M\\xf3dulos da Assinatura\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 680,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                onClick: ()=>setShowModuleModal(true),\n                                moduleColor: \"admin\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 688,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Adicionar M\\xf3dulo\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 683,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 679,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: subscription.modules.map((module)=>{\n                            var _plans_modules_module_moduleType, _plans_modules;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border rounded-lg \".concat(module.active ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20' : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: (plans === null || plans === void 0 ? void 0 : (_plans_modules = plans.modules) === null || _plans_modules === void 0 ? void 0 : (_plans_modules_module_moduleType = _plans_modules[module.moduleType]) === null || _plans_modules_module_moduleType === void 0 ? void 0 : _plans_modules_module_moduleType.name) || module.moduleType\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 704,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            module.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 708,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 710,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 703,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-3\",\n                                        children: [\n                                            formatCurrency(module.pricePerMonth),\n                                            \"/m\\xeas\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 713,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    module.active && ![\n                                        'BASIC',\n                                        'ADMIN',\n                                        'SCHEDULING'\n                                    ].includes(module.moduleType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>handleRemoveModule(module.moduleType),\n                                        disabled: actionLoading,\n                                        className: \"w-full text-red-600 border-red-200 hover:bg-red-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 724,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            \"Remover\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 717,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, module.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 695,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 693,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 678,\n                columnNumber: 9\n            }, undefined),\n            invoices.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                moduleColor: \"admin\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                children: \"Hist\\xf3rico de Faturas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 738,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: loadData,\n                                disabled: isLoading,\n                                moduleColor: \"admin\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(isLoading ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 748,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Atualizar\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 741,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 737,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"border-b border-gray-200 dark:border-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 757,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Valor\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 760,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 763,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Vencimento\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 766,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-right py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"A\\xe7\\xf5es\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 769,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 756,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 755,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: invoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-gray-100 dark:border-gray-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-gray-900 dark:text-white\",\n                                                    children: formatDate(invoice.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-gray-900 dark:text-white\",\n                                                    children: formatCurrency(invoice.amount)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 780,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(invoice.status === 'PAID' ? 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20' : invoice.status === 'PENDING' ? 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20' : 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20'),\n                                                        children: invoice.status === 'PAID' ? 'Pago' : invoice.status === 'PENDING' ? 'Pendente' : 'Falhou'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 784,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 783,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-gray-900 dark:text-white\",\n                                                    children: formatDate(invoice.dueDate)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-right\",\n                                                    children: invoice.stripeInvoiceUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: invoice.stripeInvoiceUrl,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"inline-flex items-center text-admin-primary hover:text-admin-primary/80\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            \"Download\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 800,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, invoice.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 776,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 774,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 754,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 753,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 736,\n                columnNumber: 9\n            }, undefined),\n            subscription && subscription.status === 'ACTIVE' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                moduleColor: \"admin\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: \"A\\xe7\\xf5es R\\xe1pidas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 824,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Gerencie sua assinatura e recursos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 827,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 823,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 822,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-admin-primary/30 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-admin-primary/10 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"h-4 w-4 text-admin-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 838,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 837,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: \"Upgrade de Plano\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 840,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 836,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n                                        children: \"Acesse recursos avan\\xe7ados e aumente sua capacidade\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 842,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowUpgradeModal(true),\n                                        moduleColor: \"admin\",\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 852,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Fazer Upgrade\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 845,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 835,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-300 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4 text-blue-600 dark:text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 861,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 860,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: \"Mais Usu\\xe1rios\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 863,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 859,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n                                        children: \"Adicione mais usu\\xe1rios ao seu plano atual\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 865,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowUserModal(true),\n                                        className: \"w-full border-blue-200 text-blue-600 hover:bg-blue-50 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 874,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Comprar Usu\\xe1rios\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 868,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 858,\n                                columnNumber: 13\n                            }, undefined),\n                            !subscription.cancelAtPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-red-300 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-4 w-4 text-red-600 dark:text-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 884,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 883,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: \"Cancelar Plano\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 886,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 882,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n                                        children: \"Cancele sua assinatura a qualquer momento\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 888,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowCancelModal(true),\n                                        className: \"w-full text-red-600 border-red-200 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 897,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Cancelar\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 891,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 881,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 833,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 821,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleModal, {\n                isOpen: showCancelModal,\n                onClose: ()=>setShowCancelModal(false),\n                title: \"Cancelar Assinatura\",\n                moduleColor: \"admin\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 915,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-yellow-800 dark:text-yellow-200\",\n                                            children: \"Aten\\xe7\\xe3o!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 917,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-yellow-700 dark:text-yellow-300\",\n                                            children: \"Ao cancelar, voc\\xea perder\\xe1 acesso aos recursos pagos no final do per\\xedodo atual.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 920,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 916,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 914,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400\",\n                            children: \"Tem certeza de que deseja cancelar sua assinatura? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 926,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowCancelModal(false),\n                                    disabled: actionLoading,\n                                    moduleColor: \"admin\",\n                                    children: \"Manter Assinatura\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 931,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    onClick: handleCancelSubscription,\n                                    disabled: actionLoading,\n                                    className: \"bg-red-600 hover:bg-red-700 text-white\",\n                                    children: [\n                                        actionLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 animate-spin mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 945,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 947,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Cancelar Assinatura\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 939,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 930,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 913,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 907,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleModal, {\n                isOpen: showModuleModal,\n                onClose: ()=>setShowModuleModal(false),\n                title: \"Gerenciar M\\xf3dulos\",\n                moduleColor: \"admin\",\n                size: \"lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400\",\n                            children: \"Adicione ou remova m\\xf3dulos da sua assinatura. Os m\\xf3dulos b\\xe1sicos n\\xe3o podem ser removidos.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 964,\n                            columnNumber: 11\n                        }, undefined),\n                        (plans === null || plans === void 0 ? void 0 : plans.modules) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: Object.entries(plans.modules).map((param)=>{\n                                let [moduleType, moduleInfo] = param;\n                                var _subscription_modules;\n                                const isActive = subscription === null || subscription === void 0 ? void 0 : (_subscription_modules = subscription.modules) === null || _subscription_modules === void 0 ? void 0 : _subscription_modules.find((m)=>m.moduleType === moduleType && m.active);\n                                const isBasic = [\n                                    'BASIC',\n                                    'ADMIN',\n                                    'SCHEDULING'\n                                ].includes(moduleType);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border rounded-lg \".concat(isActive ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20' : 'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                    children: moduleInfo.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 984,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 987,\n                                                    columnNumber: 36\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 983,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-3\",\n                                            children: moduleInfo.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 990,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                    children: [\n                                                        formatCurrency(moduleInfo.monthlyPrice),\n                                                        \"/m\\xeas\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 995,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                !isBasic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                                    size: \"sm\",\n                                                    variant: isActive ? \"outline\" : \"default\",\n                                                    onClick: ()=>isActive ? handleRemoveModule(moduleType) : handleAddModule(moduleType),\n                                                    disabled: actionLoading,\n                                                    moduleColor: \"admin\",\n                                                    className: isActive ? \"text-red-600 border-red-200 hover:bg-red-50\" : \"\",\n                                                    children: actionLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1009,\n                                                        columnNumber: 29\n                                                    }, undefined) : isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1012,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            \"Remover\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1017,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            \"Adicionar\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1000,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                isBasic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: \"Inclu\\xeddo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1025,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 994,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, moduleType, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 975,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 969,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 963,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 956,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleModal, {\n                isOpen: showUpgradeModal,\n                onClose: ()=>setShowUpgradeModal(false),\n                title: \"Upgrade de Plano\",\n                moduleColor: \"admin\",\n                size: \"lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto w-16 h-16 bg-admin-primary/10 rounded-full flex items-center justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-admin-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1049,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1048,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: \"Fa\\xe7a Upgrade do Seu Plano\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1051,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Desbloqueie recursos avan\\xe7ados e aumente sua capacidade\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1054,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1047,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-2 border-admin-primary/20 rounded-lg bg-admin-primary/5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"h-6 w-6 text-admin-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1062,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-900 dark:text-white\",\n                                                    children: \"Plano Profissional\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1063,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1061,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-sm text-gray-600 dark:text-gray-400 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1067,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Usu\\xe1rios ilimitados\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1066,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1071,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Todos os m\\xf3dulos inclusos\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1070,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1075,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Suporte priorit\\xe1rio\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1074,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1079,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Relat\\xf3rios avan\\xe7ados\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1078,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1065,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-admin-primary mb-2\",\n                                                    children: \"R$ 299,90\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1084,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: \"por m\\xeas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1085,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1083,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1060,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-2 border-purple-200 rounded-lg bg-purple-50 dark:bg-purple-900/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-6 w-6 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1091,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-900 dark:text-white\",\n                                                    children: \"Plano Enterprise\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1092,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1090,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-sm text-gray-600 dark:text-gray-400 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1096,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Tudo do Profissional\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1095,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1100,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"API personalizada\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1099,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1104,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Integra\\xe7\\xe3o dedicada\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1103,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1108,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Gerente de conta\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1107,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1094,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-purple-600 mb-2\",\n                                                    children: \"R$ 599,90\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1113,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: \"por m\\xeas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1114,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1112,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1089,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1059,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowUpgradeModal(false),\n                                    disabled: actionLoading,\n                                    moduleColor: \"admin\",\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1120,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    onClick: ()=>handleUpgradePlan('professional'),\n                                    disabled: actionLoading,\n                                    moduleColor: \"admin\",\n                                    children: [\n                                        actionLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 animate-spin mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1134,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1136,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Escolher Profissional\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1128,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    onClick: ()=>handleUpgradePlan('enterprise'),\n                                    disabled: actionLoading,\n                                    moduleColor: \"admin\",\n                                    className: \"ml-2\",\n                                    children: [\n                                        actionLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 animate-spin mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1147,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1149,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Escolher Enterprise\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1140,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1119,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 1046,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1039,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleModal, {\n                isOpen: showUserModal,\n                onClose: ()=>setShowUserModal(false),\n                title: \"Comprar Mais Usu\\xe1rios\",\n                moduleColor: \"admin\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600 dark:text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1167,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1166,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: \"Adicionar Usu\\xe1rios\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1169,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Expanda sua equipe com usu\\xe1rios adicionais\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1172,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1165,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 dark:bg-gray-800 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: \"Usu\\xe1rios atuais:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1179,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-gray-900 dark:text-white\",\n                                            children: userStats.current\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1180,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1178,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: \"Limite atual:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1183,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-gray-900 dark:text-white\",\n                                            children: userStats.limit\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1184,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1182,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1177,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 dark:text-white mb-4\",\n                                            children: \"Pacotes R\\xe1pidos:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1190,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                {\n                                                    users: 10,\n                                                    popular: false\n                                                },\n                                                {\n                                                    users: 25,\n                                                    popular: true\n                                                },\n                                                {\n                                                    users: 50,\n                                                    popular: false\n                                                }\n                                            ].map((pack)=>{\n                                                // Calcular preço dinamicamente\n                                                const basePrice = 19.90;\n                                                const getDiscountByUserCount = (users)=>{\n                                                    if (users >= 200) return 40;\n                                                    if (users >= 100) return 35;\n                                                    if (users >= 50) return 25;\n                                                    if (users >= 20) return 15;\n                                                    if (users >= 5) return 10;\n                                                    return 0;\n                                                };\n                                                const discount = getDiscountByUserCount(pack.users);\n                                                const totalWithoutDiscount = pack.users * basePrice;\n                                                const discountAmount = totalWithoutDiscount * (discount / 100);\n                                                const finalPrice = totalWithoutDiscount - discountAmount;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative p-4 border-2 rounded-lg cursor-pointer transition-all \".concat(pack.popular ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 dark:border-gray-700 hover:border-blue-300'),\n                                                    onClick: ()=>handleBuyMoreUsers(pack.users),\n                                                    children: [\n                                                        pack.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-2 left-1/2 transform -translate-x-1/2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-blue-500 text-white text-xs px-2 py-1 rounded-full\",\n                                                                children: \"Mais Popular\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1226,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1225,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-gray-900 dark:text-white mb-1\",\n                                                                    children: [\n                                                                        \"+\",\n                                                                        pack.users\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1232,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-3\",\n                                                                    children: \"usu\\xe1rios\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1235,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold text-blue-600 dark:text-blue-400\",\n                                                                    children: formatCurrency(finalPrice)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1236,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                    children: \"por m\\xeas\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1239,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-green-600 dark:text-green-400 mt-1\",\n                                                                    children: [\n                                                                        discount,\n                                                                        \"% de desconto\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1241,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1231,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, pack.users, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1215,\n                                                    columnNumber: 21\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1192,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1189,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200 dark:border-gray-700 pt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 dark:text-white mb-4\",\n                                            children: \"Quantidade Personalizada:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1253,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setCustomUserCount(Math.max(1, customUserCount - 1)),\n                                                            className: \"w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 flex items-center justify-center text-gray-700 dark:text-gray-300\",\n                                                            children: \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1257,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            min: \"1\",\n                                                            max: \"500\",\n                                                            value: customUserCount,\n                                                            onChange: (e)=>setCustomUserCount(Math.max(1, parseInt(e.target.value) || 1)),\n                                                            className: \"w-20 text-center text-lg font-semibold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1264,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setCustomUserCount(Math.min(500, customUserCount + 1)),\n                                                            className: \"w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 flex items-center justify-center text-gray-700 dark:text-gray-300\",\n                                                            children: \"+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1272,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1256,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: [\n                                                            \"Pre\\xe7o estimado: \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-semibold text-blue-600 dark:text-blue-400\",\n                                                                children: (()=>{\n                                                                    const basePrice = 19.90;\n                                                                    const getDiscountByUserCount = (users)=>{\n                                                                        if (users >= 200) return 40;\n                                                                        if (users >= 100) return 35;\n                                                                        if (users >= 50) return 25;\n                                                                        if (users >= 20) return 15;\n                                                                        if (users >= 5) return 10;\n                                                                        return 0;\n                                                                    };\n                                                                    const discount = getDiscountByUserCount(customUserCount);\n                                                                    const totalWithoutDiscount = customUserCount * basePrice;\n                                                                    const discountAmount = totalWithoutDiscount * (discount / 100);\n                                                                    const finalPrice = totalWithoutDiscount - discountAmount;\n                                                                    return formatCurrency(finalPrice);\n                                                                })()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1283,\n                                                                columnNumber: 37\n                                                            }, undefined),\n                                                            \"/m\\xeas\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1282,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1281,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1255,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                            onClick: ()=>handleBuyMoreUsers(customUserCount),\n                                            disabled: actionLoading,\n                                            moduleColor: \"admin\",\n                                            className: \"w-full\",\n                                            children: [\n                                                actionLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 animate-spin mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1314,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1316,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Comprar \",\n                                                customUserCount,\n                                                \" Usu\\xe1rios\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1307,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1252,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1188,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                variant: \"outline\",\n                                onClick: ()=>setShowUserModal(false),\n                                disabled: actionLoading,\n                                moduleColor: \"admin\",\n                                children: \"Fechar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 1324,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1323,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 1164,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1158,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n        lineNumber: 527,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlansPage, \"1nhX/D7eKJ0VsJRhBsoCPt9yi+s=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = PlansPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlansPage);\nvar _c;\n$RefreshReg$(_c, \"PlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js\n"));

/***/ })

});