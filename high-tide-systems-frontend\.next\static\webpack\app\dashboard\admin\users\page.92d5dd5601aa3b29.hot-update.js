"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/users/page",{

/***/ "(app-pages-browser)/./src/services/subscriptionService.js":
/*!*********************************************!*\
  !*** ./src/services/subscriptionService.js ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   subscriptionService: () => (/* binding */ subscriptionService)\n/* harmony export */ });\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n// services/subscriptionService.js\n\nconst subscriptionService = {\n    /**\n   * Obtém os planos disponíveis\n   */ async getPlans () {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.get('/subscription/plans');\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao buscar planos:', error);\n            throw error;\n        }\n    },\n    /**\n   * Obtém informações da assinatura atual\n   */ async getSubscription () {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.get('/subscription/subscription');\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao buscar assinatura:', error);\n            throw error;\n        }\n    },\n    /**\n   * Cria uma sessão de checkout do Stripe\n   * @param {string} billingCycle - 'monthly' ou 'yearly'\n   */ async createCheckoutSession () {\n        let billingCycle = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'monthly';\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.post('/subscription/checkout', {\n                billingCycle\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao criar sessão de checkout:', error);\n            throw error;\n        }\n    },\n    /**\n   * Adiciona um módulo à assinatura\n   * @param {string} moduleType - Tipo do módulo a ser adicionado\n   */ async addModule (moduleType) {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.post('/subscription/module/add', {\n                moduleType\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao adicionar módulo:', error);\n            throw error;\n        }\n    },\n    /**\n   * Remove um módulo da assinatura\n   * @param {string} moduleType - Tipo do módulo a ser removido\n   */ async removeModule (moduleType) {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.post('/subscription/module/remove', {\n                moduleType\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao remover módulo:', error);\n            throw error;\n        }\n    },\n    /**\n   * Cancela a assinatura\n   * @param {boolean} cancelAtPeriodEnd - Se deve cancelar no final do período ou imediatamente\n   */ async cancelSubscription () {\n        let cancelAtPeriodEnd = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.post('/subscription/cancel', {\n                cancelAtPeriodEnd\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao cancelar assinatura:', error);\n            throw error;\n        }\n    },\n    /**\n   * Reativa uma assinatura cancelada\n   */ async reactivateSubscription () {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.post('/subscription/reactivate');\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao reativar assinatura:', error);\n            throw error;\n        }\n    },\n    /**\n   * Atualiza o método de pagamento\n   */ async updatePaymentMethod () {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.post('/subscription/update-payment-method');\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao atualizar método de pagamento:', error);\n            throw error;\n        }\n    },\n    /**\n   * Baixa uma fatura em PDF\n   * @param {string} invoiceId - ID da fatura\n   */ async downloadInvoice (invoiceId) {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.get(\"/subscription/invoices/\".concat(invoiceId, \"/download\"), {\n                responseType: 'blob'\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao baixar fatura:', error);\n            throw error;\n        }\n    },\n    /**\n   * Obtém as faturas da assinatura\n   */ async getInvoices () {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.get('/subscription/invoices');\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao buscar faturas:', error);\n            throw error;\n        }\n    },\n    /**\n   * Adiciona usuários à assinatura\n   * @param {number} additionalUsers - Quantidade de usuários a adicionar\n   */ async addUsers (additionalUsers) {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.post('/subscription/users/add', {\n                additionalUsers\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao adicionar usuários:', error);\n            throw error;\n        }\n    },\n    /**\n   * Faz upgrade do plano\n   * @param {string} planType - Tipo do plano (professional, enterprise)\n   * @param {number} userLimit - Novo limite de usuários\n   */ async upgradePlan (planType, userLimit) {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.post('/subscription/upgrade', {\n                planType,\n                userLimit\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Erro ao fazer upgrade do plano:', error);\n            throw error;\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/subscriptionService.js\n"));

/***/ })

});