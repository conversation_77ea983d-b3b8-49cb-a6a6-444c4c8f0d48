"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js":
/*!**************************************************!*\
  !*** ./src/app/modules/admin/plans/PlansPage.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUpCircle,BarChart3,Calendar,CheckCircle,Clock,CreditCard,Crown,DollarSign,Download,Eye,FileText,History,Loader2,Minus,Package,Plus,RefreshCw,RotateCcw,Settings,Shield,Trash2,TrendingUp,UserPlus,Users,Wallet,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/subscriptionService */ \"(app-pages-browser)/./src/services/subscriptionService.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ModuleCard */ \"(app-pages-browser)/./src/components/ui/ModuleCard.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst PlansPage = ()=>{\n    var _subscription_modules;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { addToast } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [subscription, setSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [invoices, setInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showModuleModal, setShowModuleModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUpgradeModal, setShowUpgradeModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUserModal, setShowUserModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedModule, setSelectedModule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCompanyId, setSelectedCompanyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userStats, setUserStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 0,\n        limit: 0\n    });\n    const [customUserCount, setCustomUserCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN') {\n                loadCompanies();\n            } else {\n                loadData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (selectedCompanyId) {\n                loadData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        selectedCompanyId\n    ]);\n    const loadCompanies = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.get('/companies?limit=100');\n            setCompanies(response.data.companies || []);\n        } catch (error) {\n            console.error('Erro ao carregar empresas:', error);\n            addToast('Erro ao carregar lista de empresas', 'error');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const loadData = async ()=>{\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN' && !selectedCompanyId) {\n            return;\n        }\n        try {\n            setIsLoading(true);\n            // Para SYSTEM_ADMIN, adiciona o companyId como query parameter\n            const queryParams = (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN' && selectedCompanyId ? \"?companyId=\".concat(selectedCompanyId) : '';\n            // Carregar dados com tratamento individual de erros\n            let subscriptionData = null;\n            let invoicesData = [];\n            let plansData = null;\n            let userStatsData = {\n                current: 0,\n                limit: 0\n            };\n            try {\n                subscriptionData = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.get(\"/subscription/subscription\".concat(queryParams)).then((res)=>res.data);\n            } catch (error) {\n                var _error_response;\n                if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) {\n                    // Empresa não tem assinatura - isso é normal\n                    subscriptionData = null;\n                } else {\n                    throw error; // Re-throw outros erros\n                }\n            }\n            try {\n                invoicesData = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.get(\"/subscription/invoices\".concat(queryParams)).then((res)=>res.data);\n            } catch (error) {\n                var _error_response1;\n                if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 404) {\n                    // Empresa não tem faturas - isso é normal\n                    invoicesData = {\n                        invoices: []\n                    };\n                } else {\n                    console.warn('Erro ao carregar faturas:', error);\n                    invoicesData = {\n                        invoices: []\n                    };\n                }\n            }\n            try {\n                plansData = await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.getPlans();\n            } catch (error) {\n                console.warn('Erro ao carregar planos:', error);\n                plansData = null;\n            }\n            // Carregar estatísticas de usuários\n            try {\n                var _usersResponse_data_users;\n                const usersResponse = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.get(\"/users\".concat(queryParams, \"&limit=1000\"));\n                const activeUsers = ((_usersResponse_data_users = usersResponse.data.users) === null || _usersResponse_data_users === void 0 ? void 0 : _usersResponse_data_users.filter((u)=>u.active)) || [];\n                userStatsData = {\n                    current: activeUsers.length,\n                    limit: (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.userLimit) || 50 // Usar o limite da subscription\n                };\n            } catch (error) {\n                console.warn('Erro ao carregar estatísticas de usuários:', error);\n                // Se houver erro, usar dados da subscription se disponível\n                if (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.userLimit) {\n                    userStatsData.limit = subscriptionData.userLimit;\n                }\n            }\n            setSubscription(subscriptionData);\n            setPlans(plansData);\n            setInvoices((invoicesData === null || invoicesData === void 0 ? void 0 : invoicesData.invoices) || []);\n            setUserStats(userStatsData);\n        } catch (error) {\n            console.error('Erro ao carregar dados:', error);\n            addToast('Erro ao carregar informações do plano', 'error');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddModule = async (moduleType)=>{\n        try {\n            setActionLoading(true);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.addModule(moduleType);\n            addToast('Módulo adicionado com sucesso!', 'success');\n            await loadData();\n            setShowModuleModal(false);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao adicionar módulo:', error);\n            addToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao adicionar módulo', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleRemoveModule = async (moduleType)=>{\n        try {\n            setActionLoading(true);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.removeModule(moduleType);\n            addToast('Módulo removido com sucesso!', 'success');\n            await loadData();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao remover módulo:', error);\n            addToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao remover módulo', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleCancelSubscription = async ()=>{\n        try {\n            setActionLoading(true);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.cancelSubscription();\n            addToast('Assinatura cancelada com sucesso!', 'success');\n            await loadData();\n            setShowCancelModal(false);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao cancelar assinatura:', error);\n            addToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao cancelar assinatura', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleUpgradePlan = async function(planType) {\n        let userLimit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n        try {\n            setActionLoading(true);\n            // Se não foi especificado userLimit, usar um valor baseado no plano\n            const finalUserLimit = userLimit || (planType === 'professional' ? 999 : 9999);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.upgradePlan(planType, finalUserLimit);\n            addToast(\"Plano atualizado para \".concat(planType, \" com sucesso!\"), 'success');\n            await loadData();\n            setShowUpgradeModal(false);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao fazer upgrade:', error);\n            addToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao fazer upgrade', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleBuyMoreUsers = async (additionalUsers)=>{\n        try {\n            setActionLoading(true);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.addUsers(additionalUsers);\n            addToast(\"\".concat(additionalUsers, \" usu\\xe1rios adicionais adquiridos!\"), 'success');\n            await loadData();\n            setShowUserModal(false);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao comprar usuários:', error);\n            addToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao comprar usuários', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return 'text-green-600 bg-green-100';\n            case 'TRIAL':\n                return 'text-blue-600 bg-blue-100';\n            case 'PAST_DUE':\n                return 'text-yellow-600 bg-yellow-100';\n            case 'CANCELED':\n                return 'text-red-600 bg-red-100';\n            default:\n                return 'text-gray-600 bg-gray-100';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return 'Ativo';\n            case 'TRIAL':\n                return 'Período de Teste';\n            case 'PAST_DUE':\n                return 'Pagamento em Atraso';\n            case 'CANCELED':\n                return 'Cancelado';\n            default:\n                return status;\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('pt-BR');\n    };\n    const formatCurrency = (value)=>{\n        return new Intl.NumberFormat('pt-BR', {\n            style: 'currency',\n            currency: 'BRL'\n        }).format(value);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-admin-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 275,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Se for SYSTEM_ADMIN, mostrar seletor de empresa\n    if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN') {\n        var _subscription_modules1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    description: \"Selecione uma empresa para gerenciar sua assinatura, m\\xf3dulos e faturamento\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        size: 20\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 288,\n                        columnNumber: 17\n                    }, void 0),\n                    moduleColor: \"admin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    moduleColor: \"admin\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                                        children: \"Selecionar Empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleSelect, {\n                                        value: selectedCompanyId || '',\n                                        onChange: (e)=>setSelectedCompanyId(e.target.value),\n                                        moduleColor: \"admin\",\n                                        className: \"w-full max-w-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Selecione uma empresa...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: company.id,\n                                                    children: company.name\n                                                }, company.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, undefined),\n                            !selectedCompanyId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-auto w-12 h-12 bg-admin-primary/10 rounded-full flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-admin-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 316,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: \"Selecione uma empresa acima para visualizar e gerenciar seus planos de assinatura.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 318,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 314,\n                                columnNumber: 15\n                            }, undefined),\n                            selectedCompanyId && !subscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-auto w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-yellow-600 dark:text-yellow-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 327,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                        children: \"Nenhuma Assinatura Encontrada\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 329,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: \"A empresa selecionada n\\xe3o possui uma assinatura ativa no momento.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 332,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 325,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 293,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 292,\n                    columnNumber: 9\n                }, undefined),\n                selectedCompanyId && subscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                moduleColor: \"admin\",\n                                className: \"lg:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-admin-primary/10 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5 text-admin-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                                children: \"Plano da Empresa\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                children: subscription.billingCycle === 'YEARLY' ? 'Faturamento Anual' : 'Faturamento Mensal'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 348,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(getStatusColor(subscription.status)),\n                                                children: getStatusText(subscription.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 347,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                                                children: \"Valor Mensal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                                children: formatCurrency(subscription.pricePerMonth)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                                                children: \"Pr\\xf3ximo Pagamento\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                                children: subscription.nextBillingDate ? formatDate(subscription.nextBillingDate) : 'N/A'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 367,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                                            children: \"Data de In\\xedcio\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: formatDate(subscription.startDate)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 366,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    subscription.cancelAtPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-yellow-800 dark:text-yellow-200\",\n                                                            children: \"Cancelamento Agendado\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-yellow-700 dark:text-yellow-300\",\n                                                            children: \"A assinatura desta empresa ser\\xe1 cancelada no final do per\\xedodo atual.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 393,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 392,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                moduleColor: \"admin\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mx-auto w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 412,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 411,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                            children: \"Usu\\xe1rios\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 414,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-blue-600 dark:text-blue-400\",\n                                                    children: userStats.current\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: [\n                                                        \"de \",\n                                                        userStats.limit,\n                                                        \" dispon\\xedveis\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 417,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                                style: {\n                                                    width: \"\".concat(Math.min(userStats.current / userStats.limit * 100, 100), \"%\")\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 426,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 425,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 410,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 409,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                moduleColor: \"admin\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-6 w-6 text-green-600 dark:text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 438,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 437,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                            children: \"M\\xf3dulos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 440,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-green-600 dark:text-green-400 mb-4\",\n                                            children: ((_subscription_modules1 = subscription.modules) === null || _subscription_modules1 === void 0 ? void 0 : _subscription_modules1.filter((m)=>m.active).length) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 443,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setShowModuleModal(true),\n                                            moduleColor: \"admin\",\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Gerenciar\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 446,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 436,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 435,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 344,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 284,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleHeader, {\n                title: \"Gerenciamento de Planos\",\n                description: \"Gerencie sua assinatura, m\\xf3dulos e faturamento\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: 20\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 470,\n                    columnNumber: 15\n                }, void 0),\n                moduleColor: \"admin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 467,\n                columnNumber: 7\n            }, undefined),\n            subscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        moduleColor: \"admin\",\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-admin-primary/10 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-admin-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                        children: \"Plano Atual\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: subscription.billingCycle === 'YEARLY' ? 'Faturamento Anual' : 'Faturamento Mensal'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(getStatusColor(subscription.status)),\n                                        children: getStatusText(subscription.status)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 493,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 479,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                                        children: \"Valor Mensal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: formatCurrency(subscription.pricePerMonth)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 500,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                                        children: \"Pr\\xf3ximo Pagamento\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: subscription.nextBillingDate ? formatDate(subscription.nextBillingDate) : 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 506,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 499,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                                        children: \"Data de In\\xedcio\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: formatDate(subscription.startDate)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 514,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>setShowUpgradeModal(true),\n                                                    moduleColor: \"admin\",\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Fazer Upgrade\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 520,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 513,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 498,\n                                columnNumber: 13\n                            }, undefined),\n                            subscription.cancelAtPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 538,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-yellow-800 dark:text-yellow-200\",\n                                                    children: \"Cancelamento Agendado\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-yellow-700 dark:text-yellow-300\",\n                                                    children: \"Sua assinatura ser\\xe1 cancelada no final do per\\xedodo atual.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 539,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 537,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 536,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 478,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        moduleColor: \"admin\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 556,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 555,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: \"Usu\\xe1rios\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 558,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-blue-600 dark:text-blue-400\",\n                                            children: userStats.current\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 562,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                \"de \",\n                                                userStats.limit,\n                                                \" dispon\\xedveis\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 565,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 561,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                        style: {\n                                            width: \"\".concat(Math.min(userStats.current / userStats.limit * 100, 100), \"%\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 570,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 569,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setShowUserModal(true),\n                                    moduleColor: \"admin\",\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 582,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Comprar Usu\\xe1rios\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 575,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 554,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 553,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        moduleColor: \"admin\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-6 w-6 text-green-600 dark:text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 592,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 591,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: \"M\\xf3dulos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 594,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-3xl font-bold text-green-600 dark:text-green-400 mb-4\",\n                                    children: ((_subscription_modules = subscription.modules) === null || _subscription_modules === void 0 ? void 0 : _subscription_modules.filter((m)=>m.active).length) || 0\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 597,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setShowModuleModal(true),\n                                    moduleColor: \"admin\",\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 607,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Gerenciar\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 600,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 590,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 589,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 476,\n                columnNumber: 9\n            }, undefined),\n            (subscription === null || subscription === void 0 ? void 0 : subscription.modules) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                moduleColor: \"admin\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                children: \"M\\xf3dulos da Assinatura\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 619,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                onClick: ()=>setShowModuleModal(true),\n                                moduleColor: \"admin\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 627,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Adicionar M\\xf3dulo\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 622,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 618,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: subscription.modules.map((module)=>{\n                            var _plans_modules_module_moduleType, _plans_modules;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border rounded-lg \".concat(module.active ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20' : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: (plans === null || plans === void 0 ? void 0 : (_plans_modules = plans.modules) === null || _plans_modules === void 0 ? void 0 : (_plans_modules_module_moduleType = _plans_modules[module.moduleType]) === null || _plans_modules_module_moduleType === void 0 ? void 0 : _plans_modules_module_moduleType.name) || module.moduleType\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 643,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            module.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 647,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 649,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 642,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-3\",\n                                        children: [\n                                            formatCurrency(module.pricePerMonth),\n                                            \"/m\\xeas\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 652,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    module.active && ![\n                                        'BASIC',\n                                        'ADMIN',\n                                        'SCHEDULING'\n                                    ].includes(module.moduleType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>handleRemoveModule(module.moduleType),\n                                        disabled: actionLoading,\n                                        className: \"w-full text-red-600 border-red-200 hover:bg-red-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 663,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            \"Remover\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 656,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, module.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 634,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 632,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 617,\n                columnNumber: 9\n            }, undefined),\n            invoices.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                moduleColor: \"admin\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                children: \"Hist\\xf3rico de Faturas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 677,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: loadData,\n                                disabled: isLoading,\n                                moduleColor: \"admin\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(isLoading ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 687,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Atualizar\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 680,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 676,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"border-b border-gray-200 dark:border-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 696,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Valor\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 699,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 702,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Vencimento\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 705,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-right py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"A\\xe7\\xf5es\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 708,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 695,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 694,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: invoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-gray-100 dark:border-gray-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-gray-900 dark:text-white\",\n                                                    children: formatDate(invoice.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 716,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-gray-900 dark:text-white\",\n                                                    children: formatCurrency(invoice.amount)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 719,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(invoice.status === 'PAID' ? 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20' : invoice.status === 'PENDING' ? 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20' : 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20'),\n                                                        children: invoice.status === 'PAID' ? 'Pago' : invoice.status === 'PENDING' ? 'Pendente' : 'Falhou'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 722,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-gray-900 dark:text-white\",\n                                                    children: formatDate(invoice.dueDate)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-right\",\n                                                    children: invoice.stripeInvoiceUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: invoice.stripeInvoiceUrl,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"inline-flex items-center text-admin-primary hover:text-admin-primary/80\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 745,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            \"Download\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 737,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, invoice.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 715,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 713,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 693,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 692,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 675,\n                columnNumber: 9\n            }, undefined),\n            subscription && subscription.status === 'ACTIVE' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                moduleColor: \"admin\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: \"A\\xe7\\xf5es R\\xe1pidas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 763,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Gerencie sua assinatura e recursos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 766,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 762,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 761,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-admin-primary/30 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-admin-primary/10 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"h-4 w-4 text-admin-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 776,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: \"Upgrade de Plano\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 779,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 775,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n                                        children: \"Acesse recursos avan\\xe7ados e aumente sua capacidade\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 781,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowUpgradeModal(true),\n                                        moduleColor: \"admin\",\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 791,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Fazer Upgrade\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 784,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 774,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-300 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4 text-blue-600 dark:text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 799,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: \"Mais Usu\\xe1rios\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 802,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 798,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n                                        children: \"Adicione mais usu\\xe1rios ao seu plano atual\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 804,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowUserModal(true),\n                                        className: \"w-full border-blue-200 text-blue-600 hover:bg-blue-50 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 813,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Comprar Usu\\xe1rios\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 807,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 797,\n                                columnNumber: 13\n                            }, undefined),\n                            !subscription.cancelAtPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-red-300 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-4 w-4 text-red-600 dark:text-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 823,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 822,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: \"Cancelar Plano\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 825,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 821,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n                                        children: \"Cancele sua assinatura a qualquer momento\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 827,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowCancelModal(true),\n                                        className: \"w-full text-red-600 border-red-200 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 836,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Cancelar\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 830,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 820,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 772,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 760,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleModal, {\n                isOpen: showCancelModal,\n                onClose: ()=>setShowCancelModal(false),\n                title: \"Cancelar Assinatura\",\n                moduleColor: \"admin\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 854,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-yellow-800 dark:text-yellow-200\",\n                                            children: \"Aten\\xe7\\xe3o!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 856,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-yellow-700 dark:text-yellow-300\",\n                                            children: \"Ao cancelar, voc\\xea perder\\xe1 acesso aos recursos pagos no final do per\\xedodo atual.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 859,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 855,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 853,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400\",\n                            children: \"Tem certeza de que deseja cancelar sua assinatura? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 865,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowCancelModal(false),\n                                    disabled: actionLoading,\n                                    moduleColor: \"admin\",\n                                    children: \"Manter Assinatura\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 870,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    onClick: handleCancelSubscription,\n                                    disabled: actionLoading,\n                                    className: \"bg-red-600 hover:bg-red-700 text-white\",\n                                    children: [\n                                        actionLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 animate-spin mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 884,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 886,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Cancelar Assinatura\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 878,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 869,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 852,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 846,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleModal, {\n                isOpen: showModuleModal,\n                onClose: ()=>setShowModuleModal(false),\n                title: \"Gerenciar M\\xf3dulos\",\n                moduleColor: \"admin\",\n                size: \"lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400\",\n                            children: \"Adicione ou remova m\\xf3dulos da sua assinatura. Os m\\xf3dulos b\\xe1sicos n\\xe3o podem ser removidos.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 903,\n                            columnNumber: 11\n                        }, undefined),\n                        (plans === null || plans === void 0 ? void 0 : plans.modules) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: Object.entries(plans.modules).map((param)=>{\n                                let [moduleType, moduleInfo] = param;\n                                var _subscription_modules;\n                                const isActive = subscription === null || subscription === void 0 ? void 0 : (_subscription_modules = subscription.modules) === null || _subscription_modules === void 0 ? void 0 : _subscription_modules.find((m)=>m.moduleType === moduleType && m.active);\n                                const isBasic = [\n                                    'BASIC',\n                                    'ADMIN',\n                                    'SCHEDULING'\n                                ].includes(moduleType);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border rounded-lg \".concat(isActive ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20' : 'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                    children: moduleInfo.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 923,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 926,\n                                                    columnNumber: 36\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 922,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-3\",\n                                            children: moduleInfo.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 929,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                    children: [\n                                                        formatCurrency(moduleInfo.monthlyPrice),\n                                                        \"/m\\xeas\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 934,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                !isBasic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                                    size: \"sm\",\n                                                    variant: isActive ? \"outline\" : \"default\",\n                                                    onClick: ()=>isActive ? handleRemoveModule(moduleType) : handleAddModule(moduleType),\n                                                    disabled: actionLoading,\n                                                    moduleColor: \"admin\",\n                                                    className: isActive ? \"text-red-600 border-red-200 hover:bg-red-50\" : \"\",\n                                                    children: actionLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 948,\n                                                        columnNumber: 29\n                                                    }, undefined) : isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 951,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            \"Remover\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 956,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            \"Adicionar\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 939,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                isBasic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: \"Inclu\\xeddo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 964,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 933,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, moduleType, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 914,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 908,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 902,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 895,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleModal, {\n                isOpen: showUpgradeModal,\n                onClose: ()=>setShowUpgradeModal(false),\n                title: \"Upgrade de Plano\",\n                moduleColor: \"admin\",\n                size: \"lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto w-16 h-16 bg-admin-primary/10 rounded-full flex items-center justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-admin-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 988,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 987,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: \"Fa\\xe7a Upgrade do Seu Plano\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 990,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Desbloqueie recursos avan\\xe7ados e aumente sua capacidade\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 993,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 986,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-2 border-admin-primary/20 rounded-lg bg-admin-primary/5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"h-6 w-6 text-admin-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1001,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-900 dark:text-white\",\n                                                    children: \"Plano Profissional\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1002,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1000,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-sm text-gray-600 dark:text-gray-400 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1006,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Usu\\xe1rios ilimitados\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1005,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1010,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Todos os m\\xf3dulos inclusos\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1009,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1014,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Suporte priorit\\xe1rio\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1013,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1018,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Relat\\xf3rios avan\\xe7ados\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1017,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1004,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-admin-primary mb-2\",\n                                                    children: \"R$ 299,90\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1023,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: \"por m\\xeas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1024,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1022,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 999,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-2 border-purple-200 rounded-lg bg-purple-50 dark:bg-purple-900/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-6 w-6 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1030,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-900 dark:text-white\",\n                                                    children: \"Plano Enterprise\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1031,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1029,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-sm text-gray-600 dark:text-gray-400 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1035,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Tudo do Profissional\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1034,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1039,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"API personalizada\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1038,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1043,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Integra\\xe7\\xe3o dedicada\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1042,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1047,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Gerente de conta\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1046,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1033,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-purple-600 mb-2\",\n                                                    children: \"R$ 599,90\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1052,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: \"por m\\xeas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1053,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1051,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1028,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 998,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowUpgradeModal(false),\n                                    disabled: actionLoading,\n                                    moduleColor: \"admin\",\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1059,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    onClick: ()=>handleUpgradePlan('professional'),\n                                    disabled: actionLoading,\n                                    moduleColor: \"admin\",\n                                    children: [\n                                        actionLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 animate-spin mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1073,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1075,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Escolher Profissional\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1067,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    onClick: ()=>handleUpgradePlan('enterprise'),\n                                    disabled: actionLoading,\n                                    moduleColor: \"admin\",\n                                    className: \"ml-2\",\n                                    children: [\n                                        actionLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 animate-spin mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1086,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1088,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Escolher Enterprise\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1079,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1058,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 985,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 978,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleModal, {\n                isOpen: showUserModal,\n                onClose: ()=>setShowUserModal(false),\n                title: \"Comprar Mais Usu\\xe1rios\",\n                moduleColor: \"admin\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600 dark:text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1106,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1105,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: \"Adicionar Usu\\xe1rios\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1108,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Expanda sua equipe com usu\\xe1rios adicionais\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1111,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1104,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 dark:bg-gray-800 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: \"Usu\\xe1rios atuais:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1118,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-gray-900 dark:text-white\",\n                                            children: userStats.current\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1119,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1117,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: \"Limite atual:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1122,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-gray-900 dark:text-white\",\n                                            children: userStats.limit\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1123,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1121,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1116,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 dark:text-white mb-4\",\n                                            children: \"Pacotes R\\xe1pidos:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1129,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                {\n                                                    users: 10,\n                                                    popular: false\n                                                },\n                                                {\n                                                    users: 25,\n                                                    popular: true\n                                                },\n                                                {\n                                                    users: 50,\n                                                    popular: false\n                                                }\n                                            ].map((pack)=>{\n                                                // Calcular preço dinamicamente\n                                                const basePrice = 19.90;\n                                                const getDiscountByUserCount = (users)=>{\n                                                    if (users >= 200) return 40;\n                                                    if (users >= 100) return 35;\n                                                    if (users >= 50) return 25;\n                                                    if (users >= 20) return 15;\n                                                    if (users >= 5) return 10;\n                                                    return 0;\n                                                };\n                                                const discount = getDiscountByUserCount(pack.users);\n                                                const totalWithoutDiscount = pack.users * basePrice;\n                                                const discountAmount = totalWithoutDiscount * (discount / 100);\n                                                const finalPrice = totalWithoutDiscount - discountAmount;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative p-4 border-2 rounded-lg cursor-pointer transition-all \".concat(pack.popular ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 dark:border-gray-700 hover:border-blue-300'),\n                                                    onClick: ()=>handleBuyMoreUsers(pack.users),\n                                                    children: [\n                                                        pack.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-2 left-1/2 transform -translate-x-1/2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-blue-500 text-white text-xs px-2 py-1 rounded-full\",\n                                                                children: \"Mais Popular\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1165,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1164,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-gray-900 dark:text-white mb-1\",\n                                                                    children: [\n                                                                        \"+\",\n                                                                        pack.users\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1171,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-3\",\n                                                                    children: \"usu\\xe1rios\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1174,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold text-blue-600 dark:text-blue-400\",\n                                                                    children: formatCurrency(finalPrice)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1175,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                    children: \"por m\\xeas\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1178,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-green-600 dark:text-green-400 mt-1\",\n                                                                    children: [\n                                                                        discount,\n                                                                        \"% de desconto\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1180,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1170,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, pack.users, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1154,\n                                                    columnNumber: 21\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1131,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1128,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200 dark:border-gray-700 pt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 dark:text-white mb-4\",\n                                            children: \"Quantidade Personalizada:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1192,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setCustomUserCount(Math.max(1, customUserCount - 1)),\n                                                            className: \"w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 flex items-center justify-center text-gray-700 dark:text-gray-300\",\n                                                            children: \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1196,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            min: \"1\",\n                                                            max: \"500\",\n                                                            value: customUserCount,\n                                                            onChange: (e)=>setCustomUserCount(Math.max(1, parseInt(e.target.value) || 1)),\n                                                            className: \"w-20 text-center text-lg font-semibold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1203,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setCustomUserCount(Math.min(500, customUserCount + 1)),\n                                                            className: \"w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 flex items-center justify-center text-gray-700 dark:text-gray-300\",\n                                                            children: \"+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1211,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1195,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: [\n                                                            \"Pre\\xe7o estimado: \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-semibold text-blue-600 dark:text-blue-400\",\n                                                                children: (()=>{\n                                                                    const basePrice = 19.90;\n                                                                    const getDiscountByUserCount = (users)=>{\n                                                                        if (users >= 200) return 40;\n                                                                        if (users >= 100) return 35;\n                                                                        if (users >= 50) return 25;\n                                                                        if (users >= 20) return 15;\n                                                                        if (users >= 5) return 10;\n                                                                        return 0;\n                                                                    };\n                                                                    const discount = getDiscountByUserCount(customUserCount);\n                                                                    const totalWithoutDiscount = customUserCount * basePrice;\n                                                                    const discountAmount = totalWithoutDiscount * (discount / 100);\n                                                                    const finalPrice = totalWithoutDiscount - discountAmount;\n                                                                    return formatCurrency(finalPrice);\n                                                                })()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1222,\n                                                                columnNumber: 37\n                                                            }, undefined),\n                                                            \"/m\\xeas\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1221,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1220,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1194,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                            onClick: ()=>handleBuyMoreUsers(customUserCount),\n                                            disabled: actionLoading,\n                                            moduleColor: \"admin\",\n                                            className: \"w-full\",\n                                            children: [\n                                                actionLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 animate-spin mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1253,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUpCircle_BarChart3_Calendar_CheckCircle_Clock_CreditCard_Crown_DollarSign_Download_Eye_FileText_History_Loader2_Minus_Package_Plus_RefreshCw_RotateCcw_Settings_Shield_Trash2_TrendingUp_UserPlus_Users_Wallet_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1255,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Comprar \",\n                                                customUserCount,\n                                                \" Usu\\xe1rios\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1246,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1191,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1127,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                variant: \"outline\",\n                                onClick: ()=>setShowUserModal(false),\n                                disabled: actionLoading,\n                                moduleColor: \"admin\",\n                                children: \"Fechar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 1263,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1262,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 1103,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1097,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n        lineNumber: 466,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlansPage, \"+Fv396XSkv797Zsp/Jo55hEr630=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = PlansPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlansPage);\nvar _c;\n$RefreshReg$(_c, \"PlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbW9kdWxlcy9hZG1pbi9wbGFucy9QbGFuc1BhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVtRDtBQStCN0I7QUFDK0M7QUFDcEI7QUFDRTtBQUNqQjtBQUNzRDtBQUNwQztBQUVwRCxNQUFNeUMsWUFBWTtRQTRpQkRDOztJQTNpQmYsTUFBTSxFQUFFQyxJQUFJLEVBQUUsR0FBR1YsOERBQU9BO0lBQ3hCLE1BQU0sRUFBRVcsUUFBUSxFQUFFLEdBQUdWLGdFQUFRQTtJQUM3QixNQUFNLENBQUNXLFdBQVdDLGFBQWEsR0FBRzdDLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3lDLGNBQWNLLGdCQUFnQixHQUFHOUMsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDK0MsT0FBT0MsU0FBUyxHQUFHaEQsK0NBQVFBLENBQUM7SUFDbkMsTUFBTSxDQUFDaUQsVUFBVUMsWUFBWSxHQUFHbEQsK0NBQVFBLENBQUMsRUFBRTtJQUMzQyxNQUFNLENBQUNtRCxpQkFBaUJDLG1CQUFtQixHQUFHcEQsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDcUQsaUJBQWlCQyxtQkFBbUIsR0FBR3RELCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ3VELGtCQUFrQkMsb0JBQW9CLEdBQUd4RCwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUN5RCxlQUFlQyxpQkFBaUIsR0FBRzFELCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQzJELGdCQUFnQkMsa0JBQWtCLEdBQUc1RCwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUM2RCxlQUFlQyxpQkFBaUIsR0FBRzlELCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQytELFdBQVdDLGFBQWEsR0FBR2hFLCtDQUFRQSxDQUFDLEVBQUU7SUFDN0MsTUFBTSxDQUFDaUUsbUJBQW1CQyxxQkFBcUIsR0FBR2xFLCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQ21FLFdBQVdDLGFBQWEsR0FBR3BFLCtDQUFRQSxDQUFDO1FBQUVxRSxTQUFTO1FBQUdDLE9BQU87SUFBRTtJQUNsRSxNQUFNLENBQUNDLGlCQUFpQkMsbUJBQW1CLEdBQUd4RSwrQ0FBUUEsQ0FBQztJQUV2REMsZ0RBQVNBOytCQUFDO1lBQ1IsSUFBSXlDLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTStCLElBQUksTUFBSyxnQkFBZ0I7Z0JBQ2pDQztZQUNGLE9BQU87Z0JBQ0xDO1lBQ0Y7UUFDRjs4QkFBRyxFQUFFO0lBRUwxRSxnREFBU0E7K0JBQUM7WUFDUixJQUFJZ0UsbUJBQW1CO2dCQUNyQlU7WUFDRjtRQUNGOzhCQUFHO1FBQUNWO0tBQWtCO0lBRXRCLE1BQU1TLGdCQUFnQjtRQUNwQixJQUFJO1lBQ0Y3QixhQUFhO1lBQ2IsTUFBTStCLFdBQVcsTUFBTTFDLDJDQUFHQSxDQUFDMkMsR0FBRyxDQUFDO1lBQy9CYixhQUFhWSxTQUFTRSxJQUFJLENBQUNmLFNBQVMsSUFBSSxFQUFFO1FBQzVDLEVBQUUsT0FBT2dCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUNwQyxTQUFTLHNDQUFzQztRQUNqRCxTQUFVO1lBQ1JFLGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTThCLFdBQVc7UUFDZixJQUFJakMsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNK0IsSUFBSSxNQUFLLGtCQUFrQixDQUFDUixtQkFBbUI7WUFDdkQ7UUFDRjtRQUVBLElBQUk7WUFDRnBCLGFBQWE7WUFFYiwrREFBK0Q7WUFDL0QsTUFBTW9DLGNBQWN2QyxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU0rQixJQUFJLE1BQUssa0JBQWtCUixvQkFDakQsY0FBZ0MsT0FBbEJBLHFCQUNkO1lBRUosb0RBQW9EO1lBQ3BELElBQUlpQixtQkFBbUI7WUFDdkIsSUFBSUMsZUFBZSxFQUFFO1lBQ3JCLElBQUlDLFlBQVk7WUFDaEIsSUFBSUMsZ0JBQWdCO2dCQUFFaEIsU0FBUztnQkFBR0MsT0FBTztZQUFFO1lBRTNDLElBQUk7Z0JBQ0ZZLG1CQUFtQixNQUFNaEQsMkNBQUdBLENBQUMyQyxHQUFHLENBQUMsNkJBQXlDLE9BQVpJLGNBQWVLLElBQUksQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSVQsSUFBSTtZQUNuRyxFQUFFLE9BQU9DLE9BQU87b0JBQ1ZBO2dCQUFKLElBQUlBLEVBQUFBLGtCQUFBQSxNQUFNSCxRQUFRLGNBQWRHLHNDQUFBQSxnQkFBZ0JTLE1BQU0sTUFBSyxLQUFLO29CQUNsQyw2Q0FBNkM7b0JBQzdDTixtQkFBbUI7Z0JBQ3JCLE9BQU87b0JBQ0wsTUFBTUgsT0FBTyx3QkFBd0I7Z0JBQ3ZDO1lBQ0Y7WUFFQSxJQUFJO2dCQUNGSSxlQUFlLE1BQU1qRCwyQ0FBR0EsQ0FBQzJDLEdBQUcsQ0FBQyx5QkFBcUMsT0FBWkksY0FBZUssSUFBSSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJVCxJQUFJO1lBQzNGLEVBQUUsT0FBT0MsT0FBTztvQkFDVkE7Z0JBQUosSUFBSUEsRUFBQUEsbUJBQUFBLE1BQU1ILFFBQVEsY0FBZEcsdUNBQUFBLGlCQUFnQlMsTUFBTSxNQUFLLEtBQUs7b0JBQ2xDLDBDQUEwQztvQkFDMUNMLGVBQWU7d0JBQUVsQyxVQUFVLEVBQUU7b0JBQUM7Z0JBQ2hDLE9BQU87b0JBQ0wrQixRQUFRUyxJQUFJLENBQUMsNkJBQTZCVjtvQkFDMUNJLGVBQWU7d0JBQUVsQyxVQUFVLEVBQUU7b0JBQUM7Z0JBQ2hDO1lBQ0Y7WUFFQSxJQUFJO2dCQUNGbUMsWUFBWSxNQUFNckQsOEVBQW1CQSxDQUFDMkQsUUFBUTtZQUNoRCxFQUFFLE9BQU9YLE9BQU87Z0JBQ2RDLFFBQVFTLElBQUksQ0FBQyw0QkFBNEJWO2dCQUN6Q0ssWUFBWTtZQUNkO1lBRUEsb0NBQW9DO1lBQ3BDLElBQUk7b0JBRWtCTztnQkFEcEIsTUFBTUEsZ0JBQWdCLE1BQU16RCwyQ0FBR0EsQ0FBQzJDLEdBQUcsQ0FBQyxTQUFxQixPQUFaSSxhQUFZO2dCQUN6RCxNQUFNVyxjQUFjRCxFQUFBQSw0QkFBQUEsY0FBY2IsSUFBSSxDQUFDZSxLQUFLLGNBQXhCRixnREFBQUEsMEJBQTBCRyxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLE1BQU0sTUFBSyxFQUFFO2dCQUN6RVgsZ0JBQWdCO29CQUNkaEIsU0FBU3VCLFlBQVlLLE1BQU07b0JBQzNCM0IsT0FBT1ksQ0FBQUEsNkJBQUFBLHVDQUFBQSxpQkFBa0JnQixTQUFTLEtBQUksR0FBRyxnQ0FBZ0M7Z0JBQzNFO1lBQ0YsRUFBRSxPQUFPbkIsT0FBTztnQkFDZEMsUUFBUVMsSUFBSSxDQUFDLDhDQUE4Q1Y7Z0JBQzNELDJEQUEyRDtnQkFDM0QsSUFBSUcsNkJBQUFBLHVDQUFBQSxpQkFBa0JnQixTQUFTLEVBQUU7b0JBQy9CYixjQUFjZixLQUFLLEdBQUdZLGlCQUFpQmdCLFNBQVM7Z0JBQ2xEO1lBQ0Y7WUFFQXBELGdCQUFnQm9DO1lBQ2hCbEMsU0FBU29DO1lBQ1RsQyxZQUFZaUMsQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjbEMsUUFBUSxLQUFJLEVBQUU7WUFDeENtQixhQUFhaUI7UUFDZixFQUFFLE9BQU9OLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7WUFDekNwQyxTQUFTLHlDQUF5QztRQUNwRCxTQUFVO1lBQ1JFLGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTXNELGtCQUFrQixPQUFPQztRQUM3QixJQUFJO1lBQ0Z0QyxpQkFBaUI7WUFDakIsTUFBTS9CLDhFQUFtQkEsQ0FBQ3NFLFNBQVMsQ0FBQ0Q7WUFDcEN6RCxTQUFTLGtDQUFrQztZQUMzQyxNQUFNZ0M7WUFDTnJCLG1CQUFtQjtRQUNyQixFQUFFLE9BQU95QixPQUFPO2dCQUVMQSxzQkFBQUE7WUFEVEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkE7WUFDM0NwQyxTQUFTb0MsRUFBQUEsa0JBQUFBLE1BQU1ILFFBQVEsY0FBZEcsdUNBQUFBLHVCQUFBQSxnQkFBZ0JELElBQUksY0FBcEJDLDJDQUFBQSxxQkFBc0J1QixPQUFPLEtBQUksNEJBQTRCO1FBQ3hFLFNBQVU7WUFDUnhDLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsTUFBTXlDLHFCQUFxQixPQUFPSDtRQUNoQyxJQUFJO1lBQ0Z0QyxpQkFBaUI7WUFDakIsTUFBTS9CLDhFQUFtQkEsQ0FBQ3lFLFlBQVksQ0FBQ0o7WUFDdkN6RCxTQUFTLGdDQUFnQztZQUN6QyxNQUFNZ0M7UUFDUixFQUFFLE9BQU9JLE9BQU87Z0JBRUxBLHNCQUFBQTtZQURUQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtZQUN6Q3BDLFNBQVNvQyxFQUFBQSxrQkFBQUEsTUFBTUgsUUFBUSxjQUFkRyx1Q0FBQUEsdUJBQUFBLGdCQUFnQkQsSUFBSSxjQUFwQkMsMkNBQUFBLHFCQUFzQnVCLE9BQU8sS0FBSSwwQkFBMEI7UUFDdEUsU0FBVTtZQUNSeEMsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNMkMsMkJBQTJCO1FBQy9CLElBQUk7WUFDRjNDLGlCQUFpQjtZQUNqQixNQUFNL0IsOEVBQW1CQSxDQUFDMkUsa0JBQWtCO1lBQzVDL0QsU0FBUyxxQ0FBcUM7WUFDOUMsTUFBTWdDO1lBQ052QixtQkFBbUI7UUFDckIsRUFBRSxPQUFPMkIsT0FBTztnQkFFTEEsc0JBQUFBO1lBRFRDLFFBQVFELEtBQUssQ0FBQyxnQ0FBZ0NBO1lBQzlDcEMsU0FBU29DLEVBQUFBLGtCQUFBQSxNQUFNSCxRQUFRLGNBQWRHLHVDQUFBQSx1QkFBQUEsZ0JBQWdCRCxJQUFJLGNBQXBCQywyQ0FBQUEscUJBQXNCdUIsT0FBTyxLQUFJLCtCQUErQjtRQUMzRSxTQUFVO1lBQ1J4QyxpQkFBaUI7UUFDbkI7SUFDRjtJQUVBLE1BQU02QyxvQkFBb0IsZUFBT0M7WUFBVVYsNkVBQVk7UUFDckQsSUFBSTtZQUNGcEMsaUJBQWlCO1lBRWpCLG9FQUFvRTtZQUNwRSxNQUFNK0MsaUJBQWlCWCxhQUFjVSxDQUFBQSxhQUFhLGlCQUFpQixNQUFNLElBQUc7WUFFNUUsTUFBTTdFLDhFQUFtQkEsQ0FBQytFLFdBQVcsQ0FBQ0YsVUFBVUM7WUFDaERsRSxTQUFTLHlCQUFrQyxPQUFUaUUsVUFBUyxrQkFBZ0I7WUFDM0QsTUFBTWpDO1lBQ05uQixvQkFBb0I7UUFDdEIsRUFBRSxPQUFPdUIsT0FBTztnQkFFTEEsc0JBQUFBO1lBRFRDLFFBQVFELEtBQUssQ0FBQywwQkFBMEJBO1lBQ3hDcEMsU0FBU29DLEVBQUFBLGtCQUFBQSxNQUFNSCxRQUFRLGNBQWRHLHVDQUFBQSx1QkFBQUEsZ0JBQWdCRCxJQUFJLGNBQXBCQywyQ0FBQUEscUJBQXNCdUIsT0FBTyxLQUFJLHlCQUF5QjtRQUNyRSxTQUFVO1lBQ1J4QyxpQkFBaUI7UUFDbkI7SUFDRjtJQUVBLE1BQU1pRCxxQkFBcUIsT0FBT0M7UUFDaEMsSUFBSTtZQUNGbEQsaUJBQWlCO1lBQ2pCLE1BQU0vQiw4RUFBbUJBLENBQUNrRixRQUFRLENBQUNEO1lBQ25DckUsU0FBUyxHQUFtQixPQUFoQnFFLGlCQUFnQix3Q0FBbUM7WUFDL0QsTUFBTXJDO1lBQ05qQixpQkFBaUI7UUFDbkIsRUFBRSxPQUFPcUIsT0FBTztnQkFFTEEsc0JBQUFBO1lBRFRDLFFBQVFELEtBQUssQ0FBQyw2QkFBNkJBO1lBQzNDcEMsU0FBU29DLEVBQUFBLGtCQUFBQSxNQUFNSCxRQUFRLGNBQWRHLHVDQUFBQSx1QkFBQUEsZ0JBQWdCRCxJQUFJLGNBQXBCQywyQ0FBQUEscUJBQXNCdUIsT0FBTyxLQUFJLDRCQUE0QjtRQUN4RSxTQUFVO1lBQ1J4QyxpQkFBaUI7UUFDbkI7SUFDRjtJQUVBLE1BQU1vRCxpQkFBaUIsQ0FBQzFCO1FBQ3RCLE9BQVFBO1lBQ04sS0FBSztnQkFBVSxPQUFPO1lBQ3RCLEtBQUs7Z0JBQVMsT0FBTztZQUNyQixLQUFLO2dCQUFZLE9BQU87WUFDeEIsS0FBSztnQkFBWSxPQUFPO1lBQ3hCO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLE1BQU0yQixnQkFBZ0IsQ0FBQzNCO1FBQ3JCLE9BQVFBO1lBQ04sS0FBSztnQkFBVSxPQUFPO1lBQ3RCLEtBQUs7Z0JBQVMsT0FBTztZQUNyQixLQUFLO2dCQUFZLE9BQU87WUFDeEIsS0FBSztnQkFBWSxPQUFPO1lBQ3hCO2dCQUFTLE9BQU9BO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNNEIsYUFBYSxDQUFDQztRQUNsQixPQUFPLElBQUlDLEtBQUtELFlBQVlFLGtCQUFrQixDQUFDO0lBQ2pEO0lBRUEsTUFBTUMsaUJBQWlCLENBQUNDO1FBQ3RCLE9BQU8sSUFBSUMsS0FBS0MsWUFBWSxDQUFDLFNBQVM7WUFDcENDLE9BQU87WUFDUEMsVUFBVTtRQUNaLEdBQUdDLE1BQU0sQ0FBQ0w7SUFDWjtJQUVBLElBQUk3RSxXQUFXO1FBQ2IscUJBQ0UsOERBQUNtRjtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDcEgsZ1VBQU9BO2dCQUFDb0gsV0FBVTs7Ozs7Ozs7Ozs7SUFHekI7SUFFQSxrREFBa0Q7SUFDbEQsSUFBSXRGLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTStCLElBQUksTUFBSyxnQkFBZ0I7WUFrS2hCaEM7UUFqS2pCLHFCQUNFLDhEQUFDc0Y7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUM3Rix3REFBWUE7b0JBQ1g4RixPQUFNO29CQUNOQyxhQUFZO29CQUNaQyxvQkFBTSw4REFBQ2pJLGdVQUFVQTt3QkFBQ2tJLE1BQU07Ozs7OztvQkFDeEJDLGFBQVk7Ozs7Ozs4QkFHZCw4REFBQzlGLGlFQUFVQTtvQkFBQzhGLGFBQVk7OEJBQ3RCLDRFQUFDTjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEOztrREFDQyw4REFBQ087d0NBQUdOLFdBQVU7a0RBQTJEOzs7Ozs7a0RBR3pFLDhEQUFDMUYsd0RBQVlBO3dDQUNYbUYsT0FBT3hELHFCQUFxQjt3Q0FDNUJzRSxVQUFVLENBQUNDLElBQU10RSxxQkFBcUJzRSxFQUFFQyxNQUFNLENBQUNoQixLQUFLO3dDQUNwRFksYUFBWTt3Q0FDWkwsV0FBVTs7MERBRVYsOERBQUNVO2dEQUFPakIsT0FBTTswREFBRzs7Ozs7OzRDQUNoQjFELFVBQVU0RSxHQUFHLENBQUMsQ0FBQ0Msd0JBQ2QsOERBQUNGO29EQUF3QmpCLE9BQU9tQixRQUFRQyxFQUFFOzhEQUN2Q0QsUUFBUUUsSUFBSTttREFERkYsUUFBUUMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBTzVCLENBQUM1RSxtQ0FDQSw4REFBQzhEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUN6SCxpVUFBV0E7NENBQUN5SCxXQUFVOzs7Ozs7Ozs7OztrREFFekIsOERBQUNlO3dDQUFFZixXQUFVO2tEQUFtQzs7Ozs7Ozs7Ozs7OzRCQU1uRC9ELHFCQUFxQixDQUFDeEIsOEJBQ3JCLDhEQUFDc0Y7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ3pILGlVQUFXQTs0Q0FBQ3lILFdBQVU7Ozs7Ozs7Ozs7O2tEQUV6Qiw4REFBQ2dCO3dDQUFHaEIsV0FBVTtrREFBMkQ7Ozs7OztrREFHekUsOERBQUNlO3dDQUFFZixXQUFVO2tEQUFtQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBU3ZEL0QscUJBQXFCeEIsOEJBQ3BCOzhCQUVFLDRFQUFDc0Y7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDekYsaUVBQVVBO2dDQUFDOEYsYUFBWTtnQ0FBUUwsV0FBVTs7a0RBQ3hDLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUMzRyxpVUFBS0E7NERBQUMyRyxXQUFVOzs7Ozs7Ozs7OztrRUFFbkIsOERBQUNEOzswRUFDQyw4REFBQ087Z0VBQUdOLFdBQVU7MEVBQXNEOzs7Ozs7MEVBR3BFLDhEQUFDZTtnRUFBRWYsV0FBVTswRUFDVnZGLGFBQWF3RyxZQUFZLEtBQUssV0FBVyxzQkFBc0I7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFJdEUsOERBQUNDO2dEQUFLbEIsV0FBVyw4Q0FBa0YsT0FBcENkLGVBQWV6RSxhQUFhK0MsTUFBTTswREFDOUYyQixjQUFjMUUsYUFBYStDLE1BQU07Ozs7Ozs7Ozs7OztrREFJdEMsOERBQUN1Qzt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7OzBFQUNDLDhEQUFDZ0I7Z0VBQUVmLFdBQVU7MEVBQWdEOzs7Ozs7MEVBQzdELDhEQUFDZTtnRUFBRWYsV0FBVTswRUFDVlIsZUFBZS9FLGFBQWEwRyxhQUFhOzs7Ozs7Ozs7Ozs7a0VBRzlDLDhEQUFDcEI7OzBFQUNDLDhEQUFDZ0I7Z0VBQUVmLFdBQVU7MEVBQWdEOzs7Ozs7MEVBQzdELDhEQUFDZTtnRUFBRWYsV0FBVTswRUFDVnZGLGFBQWEyRyxlQUFlLEdBQUdoQyxXQUFXM0UsYUFBYTJHLGVBQWUsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUlqRiw4REFBQ3JCO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRDs7c0VBQ0MsOERBQUNnQjs0REFBRWYsV0FBVTtzRUFBZ0Q7Ozs7OztzRUFDN0QsOERBQUNlOzREQUFFZixXQUFVO3NFQUNWWixXQUFXM0UsYUFBYTRHLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29DQU16QzVHLGFBQWE2RyxpQkFBaUIsa0JBQzdCLDhEQUFDdkI7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ3pILGlVQUFXQTtvREFBQ3lILFdBQVU7Ozs7Ozs4REFDdkIsOERBQUNEOztzRUFDQyw4REFBQ2dCOzREQUFFZixXQUFVO3NFQUEyRDs7Ozs7O3NFQUd4RSw4REFBQ2U7NERBQUVmLFdBQVU7c0VBQStDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FVdEUsOERBQUN6RixpRUFBVUE7Z0NBQUM4RixhQUFZOzBDQUN0Qiw0RUFBQ047b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQzVILGlVQUFLQTtnREFBQzRILFdBQVU7Ozs7Ozs7Ozs7O3NEQUVuQiw4REFBQ007NENBQUdOLFdBQVU7c0RBQTJEOzs7Ozs7c0RBR3pFLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNlO29EQUFFZixXQUFVOzhEQUNWN0QsVUFBVUUsT0FBTzs7Ozs7OzhEQUVwQiw4REFBQzBFO29EQUFFZixXQUFVOzt3REFBMkM7d0RBQ2xEN0QsVUFBVUcsS0FBSzt3REFBQzs7Ozs7Ozs7Ozs7OztzREFHeEIsOERBQUN5RDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0Q7Z0RBQ0NDLFdBQVU7Z0RBQ1ZKLE9BQU87b0RBQUUyQixPQUFPLEdBQThELE9BQTNEQyxLQUFLQyxHQUFHLENBQUMsVUFBV3BGLE9BQU8sR0FBR0YsVUFBVUcsS0FBSyxHQUFJLEtBQUssTUFBSztnREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPekYsOERBQUMvQixpRUFBVUE7Z0NBQUM4RixhQUFZOzBDQUN0Qiw0RUFBQ047b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ2pILGlVQUFPQTtnREFBQ2lILFdBQVU7Ozs7Ozs7Ozs7O3NEQUVyQiw4REFBQ007NENBQUdOLFdBQVU7c0RBQTJEOzs7Ozs7c0RBR3pFLDhEQUFDZTs0Q0FBRWYsV0FBVTtzREFDVnZGLEVBQUFBLHlCQUFBQSxhQUFhaUgsT0FBTyxjQUFwQmpILDZDQUFBQSx1QkFBc0JxRCxNQUFNLENBQUM2RCxDQUFBQSxJQUFLQSxFQUFFM0QsTUFBTSxFQUFFQyxNQUFNLEtBQUk7Ozs7OztzREFFekQsOERBQUM3RCx3REFBWUE7NENBQ1h3SCxTQUFROzRDQUNSeEIsTUFBSzs0Q0FDTHlCLFNBQVMsSUFBTXZHLG1CQUFtQjs0Q0FDbEMrRSxhQUFZOzRDQUNaTCxXQUFVOzs4REFFViw4REFBQ2hILGlVQUFRQTtvREFBQ2dILFdBQVU7Ozs7OztnREFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFVdkQ7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUM3Rix3REFBWUE7Z0JBQ1g4RixPQUFNO2dCQUNOQyxhQUFZO2dCQUNaQyxvQkFBTSw4REFBQ2pJLGdVQUFVQTtvQkFBQ2tJLE1BQU07Ozs7OztnQkFDeEJDLGFBQVk7Ozs7OztZQUliNUYsOEJBQ0MsOERBQUNzRjtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUN6RixpRUFBVUE7d0JBQUM4RixhQUFZO3dCQUFRTCxXQUFVOzswQ0FDeEMsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQzNHLGlVQUFLQTtvREFBQzJHLFdBQVU7Ozs7Ozs7Ozs7OzBEQUVuQiw4REFBQ0Q7O2tFQUNDLDhEQUFDTzt3REFBR04sV0FBVTtrRUFBc0Q7Ozs7OztrRUFHcEUsOERBQUNlO3dEQUFFZixXQUFVO2tFQUNWdkYsYUFBYXdHLFlBQVksS0FBSyxXQUFXLHNCQUFzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUl0RSw4REFBQ0M7d0NBQUtsQixXQUFXLDhDQUFrRixPQUFwQ2QsZUFBZXpFLGFBQWErQyxNQUFNO2tEQUM5RjJCLGNBQWMxRSxhQUFhK0MsTUFBTTs7Ozs7Ozs7Ozs7OzBDQUl0Qyw4REFBQ3VDO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDs7a0VBQ0MsOERBQUNnQjt3REFBRWYsV0FBVTtrRUFBZ0Q7Ozs7OztrRUFDN0QsOERBQUNlO3dEQUFFZixXQUFVO2tFQUNWUixlQUFlL0UsYUFBYTBHLGFBQWE7Ozs7Ozs7Ozs7OzswREFHOUMsOERBQUNwQjs7a0VBQ0MsOERBQUNnQjt3REFBRWYsV0FBVTtrRUFBZ0Q7Ozs7OztrRUFDN0QsOERBQUNlO3dEQUFFZixXQUFVO2tFQUNWdkYsYUFBYTJHLGVBQWUsR0FBR2hDLFdBQVczRSxhQUFhMkcsZUFBZSxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSWpGLDhEQUFDckI7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDs7a0VBQ0MsOERBQUNnQjt3REFBRWYsV0FBVTtrRUFBZ0Q7Ozs7OztrRUFDN0QsOERBQUNlO3dEQUFFZixXQUFVO2tFQUNWWixXQUFXM0UsYUFBYTRHLFNBQVM7Ozs7Ozs7Ozs7OzswREFHdEMsOERBQUN0QjswREFDQyw0RUFBQzNGLHdEQUFZQTtvREFDWHdILFNBQVE7b0RBQ1J4QixNQUFLO29EQUNMeUIsU0FBUyxJQUFNckcsb0JBQW9CO29EQUNuQzZFLGFBQVk7b0RBQ1pMLFdBQVU7O3NFQUVWLDhEQUFDekcsaVVBQWFBOzREQUFDeUcsV0FBVTs7Ozs7O3dEQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQU9qRHZGLGFBQWE2RyxpQkFBaUIsa0JBQzdCLDhEQUFDdkI7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3pILGlVQUFXQTs0Q0FBQ3lILFdBQVU7Ozs7OztzREFDdkIsOERBQUNEOzs4REFDQyw4REFBQ2dCO29EQUFFZixXQUFVOzhEQUEyRDs7Ozs7OzhEQUd4RSw4REFBQ2U7b0RBQUVmLFdBQVU7OERBQStDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FVdEUsOERBQUN6RixpRUFBVUE7d0JBQUM4RixhQUFZO2tDQUN0Qiw0RUFBQ047NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQzVILGlVQUFLQTt3Q0FBQzRILFdBQVU7Ozs7Ozs7Ozs7OzhDQUVuQiw4REFBQ007b0NBQUdOLFdBQVU7OENBQTJEOzs7Ozs7OENBR3pFLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNlOzRDQUFFZixXQUFVO3NEQUNWN0QsVUFBVUUsT0FBTzs7Ozs7O3NEQUVwQiw4REFBQzBFOzRDQUFFZixXQUFVOztnREFBMkM7Z0RBQ2xEN0QsVUFBVUcsS0FBSztnREFBQzs7Ozs7Ozs7Ozs7Ozs4Q0FHeEIsOERBQUN5RDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQ0NDLFdBQVU7d0NBQ1ZKLE9BQU87NENBQUUyQixPQUFPLEdBQThELE9BQTNEQyxLQUFLQyxHQUFHLENBQUMsVUFBV3BGLE9BQU8sR0FBR0YsVUFBVUcsS0FBSyxHQUFJLEtBQUssTUFBSzt3Q0FBRzs7Ozs7Ozs7Ozs7OENBR3JGLDhEQUFDbEMsd0RBQVlBO29DQUNYd0gsU0FBUTtvQ0FDUnhCLE1BQUs7b0NBQ0x5QixTQUFTLElBQU1uRyxpQkFBaUI7b0NBQ2hDMkUsYUFBWTtvQ0FDWkwsV0FBVTs7c0RBRVYsOERBQUMxRyxpVUFBUUE7NENBQUMwRyxXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTzNDLDhEQUFDekYsaUVBQVVBO3dCQUFDOEYsYUFBWTtrQ0FDdEIsNEVBQUNOOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNqSCxpVUFBT0E7d0NBQUNpSCxXQUFVOzs7Ozs7Ozs7Ozs4Q0FFckIsOERBQUNNO29DQUFHTixXQUFVOzhDQUEyRDs7Ozs7OzhDQUd6RSw4REFBQ2U7b0NBQUVmLFdBQVU7OENBQ1Z2RixFQUFBQSx3QkFBQUEsYUFBYWlILE9BQU8sY0FBcEJqSCw0Q0FBQUEsc0JBQXNCcUQsTUFBTSxDQUFDNkQsQ0FBQUEsSUFBS0EsRUFBRTNELE1BQU0sRUFBRUMsTUFBTSxLQUFJOzs7Ozs7OENBRXpELDhEQUFDN0Qsd0RBQVlBO29DQUNYd0gsU0FBUTtvQ0FDUnhCLE1BQUs7b0NBQ0x5QixTQUFTLElBQU12RyxtQkFBbUI7b0NBQ2xDK0UsYUFBWTtvQ0FDWkwsV0FBVTs7c0RBRVYsOERBQUNoSCxpVUFBUUE7NENBQUNnSCxXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFTOUN2RixDQUFBQSx5QkFBQUEsbUNBQUFBLGFBQWNpSCxPQUFPLG1CQUNwQiw4REFBQ25ILGlFQUFVQTtnQkFBQzhGLGFBQVk7O2tDQUN0Qiw4REFBQ047d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDTTtnQ0FBR04sV0FBVTswQ0FBc0Q7Ozs7OzswQ0FHcEUsOERBQUM1Rix3REFBWUE7Z0NBQ1h5SCxTQUFTLElBQU12RyxtQkFBbUI7Z0NBQ2xDK0UsYUFBWTtnQ0FDWkQsTUFBSzs7a0RBRUwsOERBQUM1SCxpVUFBSUE7d0NBQUN3SCxXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7Ozs7Ozs7O2tDQUtyQyw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1p2RixhQUFhaUgsT0FBTyxDQUFDZixHQUFHLENBQUMsQ0FBQ21CO2dDQVdsQi9HLGtDQUFBQTtpREFWUCw4REFBQ2dGO2dDQUVDQyxXQUFXLHlCQUlWLE9BSEM4QixPQUFPOUQsTUFBTSxHQUNULDRFQUNBOztrREFHTiw4REFBQytCO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ2dCO2dEQUFHaEIsV0FBVTswREFDWGpGLENBQUFBLGtCQUFBQSw2QkFBQUEsaUJBQUFBLE1BQU8yRyxPQUFPLGNBQWQzRyxzQ0FBQUEsbUNBQUFBLGNBQWdCLENBQUMrRyxPQUFPMUQsVUFBVSxDQUFDLGNBQW5DckQsdURBQUFBLGlDQUFxQytGLElBQUksS0FBSWdCLE9BQU8xRCxVQUFVOzs7Ozs7NENBRWhFMEQsT0FBTzlELE1BQU0saUJBQ1osOERBQUMzRixpVUFBV0E7Z0RBQUMySCxXQUFVOzs7OzswRUFFdkIsOERBQUMxSCxpVUFBT0E7Z0RBQUMwSCxXQUFVOzs7Ozs7Ozs7Ozs7a0RBR3ZCLDhEQUFDZTt3Q0FBRWYsV0FBVTs7NENBQ1ZSLGVBQWVzQyxPQUFPWCxhQUFhOzRDQUFFOzs7Ozs7O29DQUV2Q1csT0FBTzlELE1BQU0sSUFBSSxDQUFDO3dDQUFDO3dDQUFTO3dDQUFTO3FDQUFhLENBQUMrRCxRQUFRLENBQUNELE9BQU8xRCxVQUFVLG1CQUM1RSw4REFBQ2hFLHdEQUFZQTt3Q0FDWHdILFNBQVE7d0NBQ1J4QixNQUFLO3dDQUNMeUIsU0FBUyxJQUFNdEQsbUJBQW1CdUQsT0FBTzFELFVBQVU7d0NBQ25ENEQsVUFBVW5HO3dDQUNWbUUsV0FBVTs7MERBRVYsOERBQUN2SCxpVUFBS0E7Z0RBQUN1SCxXQUFVOzs7Ozs7NENBQWlCOzs7Ozs7OzsrQkE1QmpDOEIsT0FBT2pCLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBdUN2QjVGLFNBQVNnRCxNQUFNLEdBQUcsbUJBQ2pCLDhEQUFDMUQsaUVBQVVBO2dCQUFDOEYsYUFBWTs7a0NBQ3RCLDhEQUFDTjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNNO2dDQUFHTixXQUFVOzBDQUFzRDs7Ozs7OzBDQUdwRSw4REFBQzVGLHdEQUFZQTtnQ0FDWHdILFNBQVE7Z0NBQ1J4QixNQUFLO2dDQUNMeUIsU0FBU2xGO2dDQUNUcUYsVUFBVXBIO2dDQUNWeUYsYUFBWTs7a0RBRVosOERBQUMxSCxpVUFBU0E7d0NBQUNxSCxXQUFXLGdCQUFnRCxPQUFoQ3BGLFlBQVksaUJBQWlCOzs7Ozs7b0NBQVE7Ozs7Ozs7Ozs7Ozs7a0NBSy9FLDhEQUFDbUY7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNpQzs0QkFBTWpDLFdBQVU7OzhDQUNmLDhEQUFDa0M7OENBQ0MsNEVBQUNDO3dDQUFHbkMsV0FBVTs7MERBQ1osOERBQUNvQztnREFBR3BDLFdBQVU7MERBQWdFOzs7Ozs7MERBRzlFLDhEQUFDb0M7Z0RBQUdwQyxXQUFVOzBEQUFnRTs7Ozs7OzBEQUc5RSw4REFBQ29DO2dEQUFHcEMsV0FBVTswREFBZ0U7Ozs7OzswREFHOUUsOERBQUNvQztnREFBR3BDLFdBQVU7MERBQWdFOzs7Ozs7MERBRzlFLDhEQUFDb0M7Z0RBQUdwQyxXQUFVOzBEQUFpRTs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBS25GLDhEQUFDcUM7OENBQ0VwSCxTQUFTMEYsR0FBRyxDQUFDLENBQUMyQix3QkFDYiw4REFBQ0g7NENBQW9CbkMsV0FBVTs7OERBQzdCLDhEQUFDdUM7b0RBQUd2QyxXQUFVOzhEQUNYWixXQUFXa0QsUUFBUUUsU0FBUzs7Ozs7OzhEQUUvQiw4REFBQ0Q7b0RBQUd2QyxXQUFVOzhEQUNYUixlQUFlOEMsUUFBUUcsTUFBTTs7Ozs7OzhEQUVoQyw4REFBQ0Y7b0RBQUd2QyxXQUFVOzhEQUNaLDRFQUFDa0I7d0RBQUtsQixXQUFXLDhDQU1oQixPQUxDc0MsUUFBUTlFLE1BQU0sS0FBSyxTQUNmLHlFQUNBOEUsUUFBUTlFLE1BQU0sS0FBSyxZQUNuQiw2RUFDQTtrRUFFSDhFLFFBQVE5RSxNQUFNLEtBQUssU0FBUyxTQUM1QjhFLFFBQVE5RSxNQUFNLEtBQUssWUFBWSxhQUFhOzs7Ozs7Ozs7Ozs4REFHakQsOERBQUMrRTtvREFBR3ZDLFdBQVU7OERBQ1haLFdBQVdrRCxRQUFRSSxPQUFPOzs7Ozs7OERBRTdCLDhEQUFDSDtvREFBR3ZDLFdBQVU7OERBQ1hzQyxRQUFRSyxnQkFBZ0Isa0JBQ3ZCLDhEQUFDQzt3REFDQ0MsTUFBTVAsUUFBUUssZ0JBQWdCO3dEQUM5QmxDLFFBQU87d0RBQ1BxQyxLQUFJO3dEQUNKOUMsV0FBVTs7MEVBRVYsOERBQUN0SCxpVUFBUUE7Z0VBQUNzSCxXQUFVOzs7Ozs7NERBQWlCOzs7Ozs7Ozs7Ozs7OzJDQTlCcENzQyxRQUFRekIsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBNEM5QnBHLGdCQUFnQkEsYUFBYStDLE1BQU0sS0FBSywwQkFDdkMsOERBQUNqRCxpRUFBVUE7Z0JBQUM4RixhQUFZOztrQ0FDdEIsOERBQUNOO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs7OENBQ0MsOERBQUNPO29DQUFHTixXQUFVOzhDQUEyRDs7Ozs7OzhDQUd6RSw4REFBQ2U7b0NBQUVmLFdBQVU7OENBQW1DOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNcEQsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDOUcsaVVBQVVBO29EQUFDOEcsV0FBVTs7Ozs7Ozs7Ozs7MERBRXhCLDhEQUFDZ0I7Z0RBQUdoQixXQUFVOzBEQUE0Qzs7Ozs7Ozs7Ozs7O2tEQUU1RCw4REFBQ2U7d0NBQUVmLFdBQVU7a0RBQWdEOzs7Ozs7a0RBRzdELDhEQUFDNUYsd0RBQVlBO3dDQUNYd0gsU0FBUTt3Q0FDUnhCLE1BQUs7d0NBQ0x5QixTQUFTLElBQU1yRyxvQkFBb0I7d0NBQ25DNkUsYUFBWTt3Q0FDWkwsV0FBVTs7MERBRVYsOERBQUN6RyxpVUFBYUE7Z0RBQUN5RyxXQUFVOzs7Ozs7NENBQWlCOzs7Ozs7Ozs7Ozs7OzBDQU05Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDMUcsaVVBQVFBO29EQUFDMEcsV0FBVTs7Ozs7Ozs7Ozs7MERBRXRCLDhEQUFDZ0I7Z0RBQUdoQixXQUFVOzBEQUE0Qzs7Ozs7Ozs7Ozs7O2tEQUU1RCw4REFBQ2U7d0NBQUVmLFdBQVU7a0RBQWdEOzs7Ozs7a0RBRzdELDhEQUFDNUYsd0RBQVlBO3dDQUNYd0gsU0FBUTt3Q0FDUnhCLE1BQUs7d0NBQ0x5QixTQUFTLElBQU1uRyxpQkFBaUI7d0NBQ2hDc0UsV0FBVTs7MERBRVYsOERBQUN4SCxpVUFBSUE7Z0RBQUN3SCxXQUFVOzs7Ozs7NENBQWlCOzs7Ozs7Ozs7Ozs7OzRCQU1wQyxDQUFDdkYsYUFBYTZHLGlCQUFpQixrQkFDOUIsOERBQUN2QjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUMvRyxpVUFBTUE7b0RBQUMrRyxXQUFVOzs7Ozs7Ozs7OzswREFFcEIsOERBQUNnQjtnREFBR2hCLFdBQVU7MERBQTRDOzs7Ozs7Ozs7Ozs7a0RBRTVELDhEQUFDZTt3Q0FBRWYsV0FBVTtrREFBZ0Q7Ozs7OztrREFHN0QsOERBQUM1Rix3REFBWUE7d0NBQ1h3SCxTQUFRO3dDQUNSeEIsTUFBSzt3Q0FDTHlCLFNBQVMsSUFBTXpHLG1CQUFtQjt3Q0FDbEM0RSxXQUFVOzswREFFViw4REFBQy9HLGlVQUFNQTtnREFBQytHLFdBQVU7Ozs7Ozs0Q0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBVS9DLDhEQUFDM0YsdURBQVdBO2dCQUNWMEksUUFBUTVIO2dCQUNSNkgsU0FBUyxJQUFNNUgsbUJBQW1CO2dCQUNsQzZFLE9BQU07Z0JBQ05JLGFBQVk7MEJBRVosNEVBQUNOO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDekgsaVVBQVdBO29DQUFDeUgsV0FBVTs7Ozs7OzhDQUN2Qiw4REFBQ0Q7O3NEQUNDLDhEQUFDZ0I7NENBQUVmLFdBQVU7c0RBQTJEOzs7Ozs7c0RBR3hFLDhEQUFDZTs0Q0FBRWYsV0FBVTtzREFBK0M7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNaEUsOERBQUNlOzRCQUFFZixXQUFVO3NDQUFtQzs7Ozs7O3NDQUloRCw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDNUYsd0RBQVlBO29DQUNYd0gsU0FBUTtvQ0FDUkMsU0FBUyxJQUFNekcsbUJBQW1CO29DQUNsQzRHLFVBQVVuRztvQ0FDVndFLGFBQVk7OENBQ2I7Ozs7Ozs4Q0FHRCw4REFBQ2pHLHdEQUFZQTtvQ0FDWHlILFNBQVNwRDtvQ0FDVHVELFVBQVVuRztvQ0FDVm1FLFdBQVU7O3dDQUVUbkUsOEJBQ0MsOERBQUNqRCxnVUFBT0E7NENBQUNvSCxXQUFVOzs7OztzRUFFbkIsOERBQUMvRyxpVUFBTUE7NENBQUMrRyxXQUFVOzs7Ozs7d0NBQ2xCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUVYsOERBQUMzRix1REFBV0E7Z0JBQ1YwSSxRQUFRMUg7Z0JBQ1IySCxTQUFTLElBQU0xSCxtQkFBbUI7Z0JBQ2xDMkUsT0FBTTtnQkFDTkksYUFBWTtnQkFDWkQsTUFBSzswQkFFTCw0RUFBQ0w7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDZTs0QkFBRWYsV0FBVTtzQ0FBbUM7Ozs7Ozt3QkFJL0NqRixDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU8yRyxPQUFPLG1CQUNiLDhEQUFDM0I7NEJBQUlDLFdBQVU7c0NBQ1ppRCxPQUFPQyxPQUFPLENBQUNuSSxNQUFNMkcsT0FBTyxFQUFFZixHQUFHLENBQUM7b0NBQUMsQ0FBQ3ZDLFlBQVkrRSxXQUFXO29DQUN6QzFJO2dDQUFqQixNQUFNMkksV0FBVzNJLHlCQUFBQSxvQ0FBQUEsd0JBQUFBLGFBQWNpSCxPQUFPLGNBQXJCakgsNENBQUFBLHNCQUF1QjRJLElBQUksQ0FBQzFCLENBQUFBLElBQUtBLEVBQUV2RCxVQUFVLEtBQUtBLGNBQWN1RCxFQUFFM0QsTUFBTTtnQ0FDekYsTUFBTXNGLFVBQVU7b0NBQUM7b0NBQVM7b0NBQVM7aUNBQWEsQ0FBQ3ZCLFFBQVEsQ0FBQzNEO2dDQUUxRCxxQkFDRSw4REFBQzJCO29DQUVDQyxXQUFXLHlCQUlWLE9BSENvRCxXQUNJLDRFQUNBOztzREFHTiw4REFBQ3JEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ2dCO29EQUFHaEIsV0FBVTs4REFDWG1ELFdBQVdyQyxJQUFJOzs7Ozs7Z0RBRWpCc0MsMEJBQVksOERBQUMvSyxpVUFBV0E7b0RBQUMySCxXQUFVOzs7Ozs7Ozs7Ozs7c0RBR3RDLDhEQUFDZTs0Q0FBRWYsV0FBVTtzREFDVm1ELFdBQVdqRCxXQUFXOzs7Ozs7c0RBR3pCLDhEQUFDSDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNrQjtvREFBS2xCLFdBQVU7O3dEQUNiUixlQUFlMkQsV0FBV0ksWUFBWTt3REFBRTs7Ozs7OztnREFHMUMsQ0FBQ0QseUJBQ0EsOERBQUNsSix3REFBWUE7b0RBQ1hnRyxNQUFLO29EQUNMd0IsU0FBU3dCLFdBQVcsWUFBWTtvREFDaEN2QixTQUFTLElBQU11QixXQUFXN0UsbUJBQW1CSCxjQUFjRCxnQkFBZ0JDO29EQUMzRTRELFVBQVVuRztvREFDVndFLGFBQVk7b0RBQ1pMLFdBQVdvRCxXQUFXLGdEQUFnRDs4REFFckV2SCw4QkFDQyw4REFBQ2pELGdVQUFPQTt3REFBQ29ILFdBQVU7Ozs7O29FQUNqQm9ELHlCQUNGOzswRUFDRSw4REFBQzNLLGlVQUFLQTtnRUFBQ3VILFdBQVU7Ozs7Ozs0REFBaUI7O3FGQUlwQzs7MEVBQ0UsOERBQUN4SCxpVUFBSUE7Z0VBQUN3SCxXQUFVOzs7Ozs7NERBQWlCOzs7Ozs7OztnREFPeENzRCx5QkFDQyw4REFBQ3BDO29EQUFLbEIsV0FBVTs4REFBMkM7Ozs7Ozs7Ozs7Ozs7bUNBakQxRDVCOzs7Ozs0QkF3RFg7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU9SLDhEQUFDL0QsdURBQVdBO2dCQUNWMEksUUFBUXhIO2dCQUNSeUgsU0FBUyxJQUFNeEgsb0JBQW9CO2dCQUNuQ3lFLE9BQU07Z0JBQ05JLGFBQVk7Z0JBQ1pELE1BQUs7MEJBRUwsNEVBQUNMO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQzNHLGlVQUFLQTt3Q0FBQzJHLFdBQVU7Ozs7Ozs7Ozs7OzhDQUVuQiw4REFBQ007b0NBQUdOLFdBQVU7OENBQTJEOzs7Ozs7OENBR3pFLDhEQUFDZTtvQ0FBRWYsV0FBVTs4Q0FBbUM7Ozs7Ozs7Ozs7OztzQ0FLbEQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUM3RyxpVUFBTUE7b0RBQUM2RyxXQUFVOzs7Ozs7OERBQ2xCLDhEQUFDZ0I7b0RBQUdoQixXQUFVOzhEQUE4Qzs7Ozs7Ozs7Ozs7O3NEQUU5RCw4REFBQ3dEOzRDQUFHeEQsV0FBVTs7OERBQ1osOERBQUN5RDtvREFBR3pELFdBQVU7O3NFQUNaLDhEQUFDM0gsaVVBQVdBOzREQUFDMkgsV0FBVTs7Ozs7O3dEQUFnQzs7Ozs7Ozs4REFHekQsOERBQUN5RDtvREFBR3pELFdBQVU7O3NFQUNaLDhEQUFDM0gsaVVBQVdBOzREQUFDMkgsV0FBVTs7Ozs7O3dEQUFnQzs7Ozs7Ozs4REFHekQsOERBQUN5RDtvREFBR3pELFdBQVU7O3NFQUNaLDhEQUFDM0gsaVVBQVdBOzREQUFDMkgsV0FBVTs7Ozs7O3dEQUFnQzs7Ozs7Ozs4REFHekQsOERBQUN5RDtvREFBR3pELFdBQVU7O3NFQUNaLDhEQUFDM0gsaVVBQVdBOzREQUFDMkgsV0FBVTs7Ozs7O3dEQUFnQzs7Ozs7Ozs7Ozs7OztzREFJM0QsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ2U7b0RBQUVmLFdBQVU7OERBQTZDOzs7Ozs7OERBQzFELDhEQUFDZTtvREFBRWYsV0FBVTs4REFBMkM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJNUQsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDNUcsaVVBQUdBO29EQUFDNEcsV0FBVTs7Ozs7OzhEQUNmLDhEQUFDZ0I7b0RBQUdoQixXQUFVOzhEQUE4Qzs7Ozs7Ozs7Ozs7O3NEQUU5RCw4REFBQ3dEOzRDQUFHeEQsV0FBVTs7OERBQ1osOERBQUN5RDtvREFBR3pELFdBQVU7O3NFQUNaLDhEQUFDM0gsaVVBQVdBOzREQUFDMkgsV0FBVTs7Ozs7O3dEQUFnQzs7Ozs7Ozs4REFHekQsOERBQUN5RDtvREFBR3pELFdBQVU7O3NFQUNaLDhEQUFDM0gsaVVBQVdBOzREQUFDMkgsV0FBVTs7Ozs7O3dEQUFnQzs7Ozs7Ozs4REFHekQsOERBQUN5RDtvREFBR3pELFdBQVU7O3NFQUNaLDhEQUFDM0gsaVVBQVdBOzREQUFDMkgsV0FBVTs7Ozs7O3dEQUFnQzs7Ozs7Ozs4REFHekQsOERBQUN5RDtvREFBR3pELFdBQVU7O3NFQUNaLDhEQUFDM0gsaVVBQVdBOzREQUFDMkgsV0FBVTs7Ozs7O3dEQUFnQzs7Ozs7Ozs7Ozs7OztzREFJM0QsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ2U7b0RBQUVmLFdBQVU7OERBQTBDOzs7Ozs7OERBQ3ZELDhEQUFDZTtvREFBRWYsV0FBVTs4REFBMkM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLOUQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzVGLHdEQUFZQTtvQ0FDWHdILFNBQVE7b0NBQ1JDLFNBQVMsSUFBTXJHLG9CQUFvQjtvQ0FDbkN3RyxVQUFVbkc7b0NBQ1Z3RSxhQUFZOzhDQUNiOzs7Ozs7OENBR0QsOERBQUNqRyx3REFBWUE7b0NBQ1h5SCxTQUFTLElBQU1sRCxrQkFBa0I7b0NBQ2pDcUQsVUFBVW5HO29DQUNWd0UsYUFBWTs7d0NBRVh4RSw4QkFDQyw4REFBQ2pELGdVQUFPQTs0Q0FBQ29ILFdBQVU7Ozs7O3NFQUVuQiw4REFBQ3pHLGlVQUFhQTs0Q0FBQ3lHLFdBQVU7Ozs7Ozt3Q0FDekI7Ozs7Ozs7OENBR0osOERBQUM1Rix3REFBWUE7b0NBQ1h5SCxTQUFTLElBQU1sRCxrQkFBa0I7b0NBQ2pDcUQsVUFBVW5HO29DQUNWd0UsYUFBWTtvQ0FDWkwsV0FBVTs7d0NBRVRuRSw4QkFDQyw4REFBQ2pELGdVQUFPQTs0Q0FBQ29ILFdBQVU7Ozs7O3NFQUVuQiw4REFBQ3pHLGlVQUFhQTs0Q0FBQ3lHLFdBQVU7Ozs7Ozt3Q0FDekI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRViw4REFBQzNGLHVEQUFXQTtnQkFDVjBJLFFBQVF0SDtnQkFDUnVILFNBQVMsSUFBTXRILGlCQUFpQjtnQkFDaEN1RSxPQUFNO2dCQUNOSSxhQUFZOzBCQUVaLDRFQUFDTjtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUM1SCxpVUFBS0E7d0NBQUM0SCxXQUFVOzs7Ozs7Ozs7Ozs4Q0FFbkIsOERBQUNNO29DQUFHTixXQUFVOzhDQUEyRDs7Ozs7OzhDQUd6RSw4REFBQ2U7b0NBQUVmLFdBQVU7OENBQW1DOzs7Ozs7Ozs7Ozs7c0NBS2xELDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ2tCOzRDQUFLbEIsV0FBVTtzREFBMkM7Ozs7OztzREFDM0QsOERBQUNrQjs0Q0FBS2xCLFdBQVU7c0RBQStDN0QsVUFBVUUsT0FBTzs7Ozs7Ozs7Ozs7OzhDQUVsRiw4REFBQzBEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ2tCOzRDQUFLbEIsV0FBVTtzREFBMkM7Ozs7OztzREFDM0QsOERBQUNrQjs0Q0FBS2xCLFdBQVU7c0RBQStDN0QsVUFBVUcsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUlsRiw4REFBQ3lEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7O3NEQUNDLDhEQUFDaUI7NENBQUdoQixXQUFVO3NEQUFpRDs7Ozs7O3NEQUUvRCw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ1o7Z0RBQ0M7b0RBQUVuQyxPQUFPO29EQUFJNkYsU0FBUztnREFBTTtnREFDNUI7b0RBQUU3RixPQUFPO29EQUFJNkYsU0FBUztnREFBSztnREFDM0I7b0RBQUU3RixPQUFPO29EQUFJNkYsU0FBUztnREFBTTs2Q0FDN0IsQ0FBQy9DLEdBQUcsQ0FBQyxDQUFDZ0Q7Z0RBQ0wsK0JBQStCO2dEQUMvQixNQUFNQyxZQUFZO2dEQUNsQixNQUFNQyx5QkFBeUIsQ0FBQ2hHO29EQUM5QixJQUFJQSxTQUFTLEtBQUssT0FBTztvREFDekIsSUFBSUEsU0FBUyxLQUFLLE9BQU87b0RBQ3pCLElBQUlBLFNBQVMsSUFBSSxPQUFPO29EQUN4QixJQUFJQSxTQUFTLElBQUksT0FBTztvREFDeEIsSUFBSUEsU0FBUyxHQUFHLE9BQU87b0RBQ3ZCLE9BQU87Z0RBQ1Q7Z0RBRUEsTUFBTWlHLFdBQVdELHVCQUF1QkYsS0FBSzlGLEtBQUs7Z0RBQ2xELE1BQU1rRyx1QkFBdUJKLEtBQUs5RixLQUFLLEdBQUcrRjtnREFDMUMsTUFBTUksaUJBQWlCRCx1QkFBd0JELENBQUFBLFdBQVcsR0FBRTtnREFDNUQsTUFBTUcsYUFBYUYsdUJBQXVCQztnREFFMUMscUJBQ0UsOERBQUNqRTtvREFFQ0MsV0FBVyxrRUFJVixPQUhDMkQsS0FBS0QsT0FBTyxHQUNSLG1EQUNBO29EQUVON0IsU0FBUyxJQUFNOUMsbUJBQW1CNEUsS0FBSzlGLEtBQUs7O3dEQUUzQzhGLEtBQUtELE9BQU8sa0JBQ1gsOERBQUMzRDs0REFBSUMsV0FBVTtzRUFDYiw0RUFBQ2tCO2dFQUFLbEIsV0FBVTswRUFBd0Q7Ozs7Ozs7Ozs7O3NFQUs1RSw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDZTtvRUFBRWYsV0FBVTs7d0VBQXdEO3dFQUNqRTJELEtBQUs5RixLQUFLOzs7Ozs7OzhFQUVkLDhEQUFDa0Q7b0VBQUVmLFdBQVU7OEVBQWdEOzs7Ozs7OEVBQzdELDhEQUFDZTtvRUFBRWYsV0FBVTs4RUFDVlIsZUFBZXlFOzs7Ozs7OEVBRWxCLDhEQUFDbEQ7b0VBQUVmLFdBQVU7OEVBQTJDOzs7Ozs7Z0VBQ3ZEOEQsV0FBVyxtQkFDViw4REFBQy9DO29FQUFFZixXQUFVOzt3RUFDVjhEO3dFQUFTOzs7Ozs7Ozs7Ozs7OzttREExQlhILEtBQUs5RixLQUFLOzs7Ozs0Q0FnQ3JCOzs7Ozs7Ozs7Ozs7OENBSUosOERBQUNrQztvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNnQjs0Q0FBR2hCLFdBQVU7c0RBQWlEOzs7Ozs7c0RBRS9ELDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ2tFOzREQUNDQyxNQUFLOzREQUNMdEMsU0FBUyxJQUFNckYsbUJBQW1CZ0YsS0FBSzRDLEdBQUcsQ0FBQyxHQUFHN0gsa0JBQWtCOzREQUNoRXlELFdBQVU7c0VBQ1g7Ozs7OztzRUFHRCw4REFBQ3FFOzREQUNDRixNQUFLOzREQUNMMUMsS0FBSTs0REFDSjJDLEtBQUk7NERBQ0ozRSxPQUFPbEQ7NERBQ1BnRSxVQUFVLENBQUNDLElBQU1oRSxtQkFBbUJnRixLQUFLNEMsR0FBRyxDQUFDLEdBQUdFLFNBQVM5RCxFQUFFQyxNQUFNLENBQUNoQixLQUFLLEtBQUs7NERBQzVFTyxXQUFVOzs7Ozs7c0VBRVosOERBQUNrRTs0REFDQ0MsTUFBSzs0REFDTHRDLFNBQVMsSUFBTXJGLG1CQUFtQmdGLEtBQUtDLEdBQUcsQ0FBQyxLQUFLbEYsa0JBQWtCOzREQUNsRXlELFdBQVU7c0VBQ1g7Ozs7Ozs7Ozs7Ozs4REFLSCw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNEO3dEQUFJQyxXQUFVOzs0REFBMkM7MEVBQ3hDLDhEQUFDa0I7Z0VBQUtsQixXQUFVOzBFQUM3QixDQUFDO29FQUNBLE1BQU00RCxZQUFZO29FQUNsQixNQUFNQyx5QkFBeUIsQ0FBQ2hHO3dFQUM5QixJQUFJQSxTQUFTLEtBQUssT0FBTzt3RUFDekIsSUFBSUEsU0FBUyxLQUFLLE9BQU87d0VBQ3pCLElBQUlBLFNBQVMsSUFBSSxPQUFPO3dFQUN4QixJQUFJQSxTQUFTLElBQUksT0FBTzt3RUFDeEIsSUFBSUEsU0FBUyxHQUFHLE9BQU87d0VBQ3ZCLE9BQU87b0VBQ1Q7b0VBRUEsTUFBTWlHLFdBQVdELHVCQUF1QnRIO29FQUN4QyxNQUFNd0gsdUJBQXVCeEgsa0JBQWtCcUg7b0VBQy9DLE1BQU1JLGlCQUFpQkQsdUJBQXdCRCxDQUFBQSxXQUFXLEdBQUU7b0VBQzVELE1BQU1HLGFBQWFGLHVCQUF1QkM7b0VBRTFDLE9BQU94RSxlQUFleUU7Z0VBQ3hCOzs7Ozs7NERBQ0s7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFLYiw4REFBQzdKLHdEQUFZQTs0Q0FDWHlILFNBQVMsSUFBTTlDLG1CQUFtQnhDOzRDQUNsQ3lGLFVBQVVuRzs0Q0FDVndFLGFBQVk7NENBQ1pMLFdBQVU7O2dEQUVUbkUsOEJBQ0MsOERBQUNqRCxnVUFBT0E7b0RBQUNvSCxXQUFVOzs7Ozs4RUFFbkIsOERBQUN4SCxpVUFBSUE7b0RBQUN3SCxXQUFVOzs7Ozs7Z0RBQ2hCO2dEQUNPekQ7Z0RBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUsvQiw4REFBQ3dEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDNUYsd0RBQVlBO2dDQUNYd0gsU0FBUTtnQ0FDUkMsU0FBUyxJQUFNbkcsaUJBQWlCO2dDQUNoQ3NHLFVBQVVuRztnQ0FDVndFLGFBQVk7MENBQ2I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRYjtHQWx0Q003Rjs7UUFDYVIsMERBQU9BO1FBQ0hDLDREQUFRQTs7O0tBRnpCTztBQW90Q04saUVBQWVBLFNBQVNBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcUHJvZ3JhbWHDp8Ojb1xcaGlnaC10aWRlLXN5c3RlbXMtZnJvbnRlbmRcXHNyY1xcYXBwXFxtb2R1bGVzXFxhZG1pblxccGxhbnNcXFBsYW5zUGFnZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7XG4gIENyZWRpdENhcmQsXG4gIENhbGVuZGFyLFxuICBVc2VycyxcbiAgQ2hlY2tDaXJjbGUsXG4gIFhDaXJjbGUsXG4gIEFsZXJ0Q2lyY2xlLFxuICBQbHVzLFxuICBNaW51cyxcbiAgRG93bmxvYWQsXG4gIFJlZnJlc2hDdyxcbiAgTG9hZGVyMixcbiAgQ2xvY2ssXG4gIERvbGxhclNpZ24sXG4gIFBhY2thZ2UsXG4gIFNldHRpbmdzLFxuICBUcmFzaDIsXG4gIFRyZW5kaW5nVXAsXG4gIFNoaWVsZCxcbiAgWmFwLFxuICBDcm93bixcbiAgVXNlclBsdXMsXG4gIEFycm93VXBDaXJjbGUsXG4gIFJvdGF0ZUNjdyxcbiAgRmlsZVRleHQsXG4gIEFjdGl2aXR5LFxuICBCYXJDaGFydDMsXG4gIFdhbGxldCxcbiAgSGlzdG9yeSxcbiAgRXllXG59IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcbmltcG9ydCB7IHN1YnNjcmlwdGlvblNlcnZpY2UgfSBmcm9tIFwiQC9zZXJ2aWNlcy9zdWJzY3JpcHRpb25TZXJ2aWNlXCI7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSBcIkAvY29udGV4dHMvQXV0aENvbnRleHRcIjtcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSBcIkAvY29udGV4dHMvVG9hc3RDb250ZXh0XCI7XG5pbXBvcnQgeyBhcGkgfSBmcm9tIFwiQC91dGlscy9hcGlcIjtcbmltcG9ydCB7IE1vZHVsZUhlYWRlciwgTW9kdWxlQnV0dG9uLCBNb2R1bGVNb2RhbCwgTW9kdWxlU2VsZWN0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aVwiO1xuaW1wb3J0IE1vZHVsZUNhcmQgZnJvbSBcIkAvY29tcG9uZW50cy91aS9Nb2R1bGVDYXJkXCI7XG5cbmNvbnN0IFBsYW5zUGFnZSA9ICgpID0+IHtcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IHsgYWRkVG9hc3QgfSA9IHVzZVRvYXN0KCk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW3N1YnNjcmlwdGlvbiwgc2V0U3Vic2NyaXB0aW9uXSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbcGxhbnMsIHNldFBsYW5zXSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbaW52b2ljZXMsIHNldEludm9pY2VzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW3Nob3dDYW5jZWxNb2RhbCwgc2V0U2hvd0NhbmNlbE1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dNb2R1bGVNb2RhbCwgc2V0U2hvd01vZHVsZU1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dVcGdyYWRlTW9kYWwsIHNldFNob3dVcGdyYWRlTW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd1VzZXJNb2RhbCwgc2V0U2hvd1VzZXJNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzZWxlY3RlZE1vZHVsZSwgc2V0U2VsZWN0ZWRNb2R1bGVdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFthY3Rpb25Mb2FkaW5nLCBzZXRBY3Rpb25Mb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2NvbXBhbmllcywgc2V0Q29tcGFuaWVzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW3NlbGVjdGVkQ29tcGFueUlkLCBzZXRTZWxlY3RlZENvbXBhbnlJZF0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW3VzZXJTdGF0cywgc2V0VXNlclN0YXRzXSA9IHVzZVN0YXRlKHsgY3VycmVudDogMCwgbGltaXQ6IDAgfSk7XG4gIGNvbnN0IFtjdXN0b21Vc2VyQ291bnQsIHNldEN1c3RvbVVzZXJDb3VudF0gPSB1c2VTdGF0ZSgxMCk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAodXNlcj8ucm9sZSA9PT0gJ1NZU1RFTV9BRE1JTicpIHtcbiAgICAgIGxvYWRDb21wYW5pZXMoKTtcbiAgICB9IGVsc2Uge1xuICAgICAgbG9hZERhdGEoKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzZWxlY3RlZENvbXBhbnlJZCkge1xuICAgICAgbG9hZERhdGEoKTtcbiAgICB9XG4gIH0sIFtzZWxlY3RlZENvbXBhbnlJZF0pO1xuXG4gIGNvbnN0IGxvYWRDb21wYW5pZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLmdldCgnL2NvbXBhbmllcz9saW1pdD0xMDAnKTtcbiAgICAgIHNldENvbXBhbmllcyhyZXNwb25zZS5kYXRhLmNvbXBhbmllcyB8fCBbXSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gY2FycmVnYXIgZW1wcmVzYXM6JywgZXJyb3IpO1xuICAgICAgYWRkVG9hc3QoJ0Vycm8gYW8gY2FycmVnYXIgbGlzdGEgZGUgZW1wcmVzYXMnLCAnZXJyb3InKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgbG9hZERhdGEgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKHVzZXI/LnJvbGUgPT09ICdTWVNURU1fQURNSU4nICYmICFzZWxlY3RlZENvbXBhbnlJZCkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG5cbiAgICAgIC8vIFBhcmEgU1lTVEVNX0FETUlOLCBhZGljaW9uYSBvIGNvbXBhbnlJZCBjb21vIHF1ZXJ5IHBhcmFtZXRlclxuICAgICAgY29uc3QgcXVlcnlQYXJhbXMgPSB1c2VyPy5yb2xlID09PSAnU1lTVEVNX0FETUlOJyAmJiBzZWxlY3RlZENvbXBhbnlJZFxuICAgICAgICA/IGA/Y29tcGFueUlkPSR7c2VsZWN0ZWRDb21wYW55SWR9YFxuICAgICAgICA6ICcnO1xuXG4gICAgICAvLyBDYXJyZWdhciBkYWRvcyBjb20gdHJhdGFtZW50byBpbmRpdmlkdWFsIGRlIGVycm9zXG4gICAgICBsZXQgc3Vic2NyaXB0aW9uRGF0YSA9IG51bGw7XG4gICAgICBsZXQgaW52b2ljZXNEYXRhID0gW107XG4gICAgICBsZXQgcGxhbnNEYXRhID0gbnVsbDtcbiAgICAgIGxldCB1c2VyU3RhdHNEYXRhID0geyBjdXJyZW50OiAwLCBsaW1pdDogMCB9O1xuXG4gICAgICB0cnkge1xuICAgICAgICBzdWJzY3JpcHRpb25EYXRhID0gYXdhaXQgYXBpLmdldChgL3N1YnNjcmlwdGlvbi9zdWJzY3JpcHRpb24ke3F1ZXJ5UGFyYW1zfWApLnRoZW4ocmVzID0+IHJlcy5kYXRhKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGlmIChlcnJvci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDQpIHtcbiAgICAgICAgICAvLyBFbXByZXNhIG7Do28gdGVtIGFzc2luYXR1cmEgLSBpc3NvIMOpIG5vcm1hbFxuICAgICAgICAgIHN1YnNjcmlwdGlvbkRhdGEgPSBudWxsO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHRocm93IGVycm9yOyAvLyBSZS10aHJvdyBvdXRyb3MgZXJyb3NcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICB0cnkge1xuICAgICAgICBpbnZvaWNlc0RhdGEgPSBhd2FpdCBhcGkuZ2V0KGAvc3Vic2NyaXB0aW9uL2ludm9pY2VzJHtxdWVyeVBhcmFtc31gKS50aGVuKHJlcyA9PiByZXMuZGF0YSk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDA0KSB7XG4gICAgICAgICAgLy8gRW1wcmVzYSBuw6NvIHRlbSBmYXR1cmFzIC0gaXNzbyDDqSBub3JtYWxcbiAgICAgICAgICBpbnZvaWNlc0RhdGEgPSB7IGludm9pY2VzOiBbXSB9O1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnNvbGUud2FybignRXJybyBhbyBjYXJyZWdhciBmYXR1cmFzOicsIGVycm9yKTtcbiAgICAgICAgICBpbnZvaWNlc0RhdGEgPSB7IGludm9pY2VzOiBbXSB9O1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIHBsYW5zRGF0YSA9IGF3YWl0IHN1YnNjcmlwdGlvblNlcnZpY2UuZ2V0UGxhbnMoKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUud2FybignRXJybyBhbyBjYXJyZWdhciBwbGFub3M6JywgZXJyb3IpO1xuICAgICAgICBwbGFuc0RhdGEgPSBudWxsO1xuICAgICAgfVxuXG4gICAgICAvLyBDYXJyZWdhciBlc3RhdMOtc3RpY2FzIGRlIHVzdcOhcmlvc1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgdXNlcnNSZXNwb25zZSA9IGF3YWl0IGFwaS5nZXQoYC91c2VycyR7cXVlcnlQYXJhbXN9JmxpbWl0PTEwMDBgKTtcbiAgICAgICAgY29uc3QgYWN0aXZlVXNlcnMgPSB1c2Vyc1Jlc3BvbnNlLmRhdGEudXNlcnM/LmZpbHRlcih1ID0+IHUuYWN0aXZlKSB8fCBbXTtcbiAgICAgICAgdXNlclN0YXRzRGF0YSA9IHtcbiAgICAgICAgICBjdXJyZW50OiBhY3RpdmVVc2Vycy5sZW5ndGgsXG4gICAgICAgICAgbGltaXQ6IHN1YnNjcmlwdGlvbkRhdGE/LnVzZXJMaW1pdCB8fCA1MCAvLyBVc2FyIG8gbGltaXRlIGRhIHN1YnNjcmlwdGlvblxuICAgICAgICB9O1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKCdFcnJvIGFvIGNhcnJlZ2FyIGVzdGF0w61zdGljYXMgZGUgdXN1w6FyaW9zOicsIGVycm9yKTtcbiAgICAgICAgLy8gU2UgaG91dmVyIGVycm8sIHVzYXIgZGFkb3MgZGEgc3Vic2NyaXB0aW9uIHNlIGRpc3BvbsOtdmVsXG4gICAgICAgIGlmIChzdWJzY3JpcHRpb25EYXRhPy51c2VyTGltaXQpIHtcbiAgICAgICAgICB1c2VyU3RhdHNEYXRhLmxpbWl0ID0gc3Vic2NyaXB0aW9uRGF0YS51c2VyTGltaXQ7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgc2V0U3Vic2NyaXB0aW9uKHN1YnNjcmlwdGlvbkRhdGEpO1xuICAgICAgc2V0UGxhbnMocGxhbnNEYXRhKTtcbiAgICAgIHNldEludm9pY2VzKGludm9pY2VzRGF0YT8uaW52b2ljZXMgfHwgW10pO1xuICAgICAgc2V0VXNlclN0YXRzKHVzZXJTdGF0c0RhdGEpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGNhcnJlZ2FyIGRhZG9zOicsIGVycm9yKTtcbiAgICAgIGFkZFRvYXN0KCdFcnJvIGFvIGNhcnJlZ2FyIGluZm9ybWHDp8O1ZXMgZG8gcGxhbm8nLCAnZXJyb3InKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQWRkTW9kdWxlID0gYXN5bmMgKG1vZHVsZVR5cGUpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0QWN0aW9uTG9hZGluZyh0cnVlKTtcbiAgICAgIGF3YWl0IHN1YnNjcmlwdGlvblNlcnZpY2UuYWRkTW9kdWxlKG1vZHVsZVR5cGUpO1xuICAgICAgYWRkVG9hc3QoJ03Ds2R1bG8gYWRpY2lvbmFkbyBjb20gc3VjZXNzbyEnLCAnc3VjY2VzcycpO1xuICAgICAgYXdhaXQgbG9hZERhdGEoKTtcbiAgICAgIHNldFNob3dNb2R1bGVNb2RhbChmYWxzZSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gYWRpY2lvbmFyIG3Ds2R1bG86JywgZXJyb3IpO1xuICAgICAgYWRkVG9hc3QoZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ0Vycm8gYW8gYWRpY2lvbmFyIG3Ds2R1bG8nLCAnZXJyb3InKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0QWN0aW9uTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVJlbW92ZU1vZHVsZSA9IGFzeW5jIChtb2R1bGVUeXBlKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldEFjdGlvbkxvYWRpbmcodHJ1ZSk7XG4gICAgICBhd2FpdCBzdWJzY3JpcHRpb25TZXJ2aWNlLnJlbW92ZU1vZHVsZShtb2R1bGVUeXBlKTtcbiAgICAgIGFkZFRvYXN0KCdNw7NkdWxvIHJlbW92aWRvIGNvbSBzdWNlc3NvIScsICdzdWNjZXNzJyk7XG4gICAgICBhd2FpdCBsb2FkRGF0YSgpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIHJlbW92ZXIgbcOzZHVsbzonLCBlcnJvcik7XG4gICAgICBhZGRUb2FzdChlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAnRXJybyBhbyByZW1vdmVyIG3Ds2R1bG8nLCAnZXJyb3InKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0QWN0aW9uTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNhbmNlbFN1YnNjcmlwdGlvbiA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0QWN0aW9uTG9hZGluZyh0cnVlKTtcbiAgICAgIGF3YWl0IHN1YnNjcmlwdGlvblNlcnZpY2UuY2FuY2VsU3Vic2NyaXB0aW9uKCk7XG4gICAgICBhZGRUb2FzdCgnQXNzaW5hdHVyYSBjYW5jZWxhZGEgY29tIHN1Y2Vzc28hJywgJ3N1Y2Nlc3MnKTtcbiAgICAgIGF3YWl0IGxvYWREYXRhKCk7XG4gICAgICBzZXRTaG93Q2FuY2VsTW9kYWwoZmFsc2UpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGNhbmNlbGFyIGFzc2luYXR1cmE6JywgZXJyb3IpO1xuICAgICAgYWRkVG9hc3QoZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ0Vycm8gYW8gY2FuY2VsYXIgYXNzaW5hdHVyYScsICdlcnJvcicpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRBY3Rpb25Mb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlVXBncmFkZVBsYW4gPSBhc3luYyAocGxhblR5cGUsIHVzZXJMaW1pdCA9IG51bGwpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0QWN0aW9uTG9hZGluZyh0cnVlKTtcblxuICAgICAgLy8gU2UgbsOjbyBmb2kgZXNwZWNpZmljYWRvIHVzZXJMaW1pdCwgdXNhciB1bSB2YWxvciBiYXNlYWRvIG5vIHBsYW5vXG4gICAgICBjb25zdCBmaW5hbFVzZXJMaW1pdCA9IHVzZXJMaW1pdCB8fCAocGxhblR5cGUgPT09ICdwcm9mZXNzaW9uYWwnID8gOTk5IDogOTk5OSk7XG5cbiAgICAgIGF3YWl0IHN1YnNjcmlwdGlvblNlcnZpY2UudXBncmFkZVBsYW4ocGxhblR5cGUsIGZpbmFsVXNlckxpbWl0KTtcbiAgICAgIGFkZFRvYXN0KGBQbGFubyBhdHVhbGl6YWRvIHBhcmEgJHtwbGFuVHlwZX0gY29tIHN1Y2Vzc28hYCwgJ3N1Y2Nlc3MnKTtcbiAgICAgIGF3YWl0IGxvYWREYXRhKCk7XG4gICAgICBzZXRTaG93VXBncmFkZU1vZGFsKGZhbHNlKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBmYXplciB1cGdyYWRlOicsIGVycm9yKTtcbiAgICAgIGFkZFRvYXN0KGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICdFcnJvIGFvIGZhemVyIHVwZ3JhZGUnLCAnZXJyb3InKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0QWN0aW9uTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUJ1eU1vcmVVc2VycyA9IGFzeW5jIChhZGRpdGlvbmFsVXNlcnMpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0QWN0aW9uTG9hZGluZyh0cnVlKTtcbiAgICAgIGF3YWl0IHN1YnNjcmlwdGlvblNlcnZpY2UuYWRkVXNlcnMoYWRkaXRpb25hbFVzZXJzKTtcbiAgICAgIGFkZFRvYXN0KGAke2FkZGl0aW9uYWxVc2Vyc30gdXN1w6FyaW9zIGFkaWNpb25haXMgYWRxdWlyaWRvcyFgLCAnc3VjY2VzcycpO1xuICAgICAgYXdhaXQgbG9hZERhdGEoKTtcbiAgICAgIHNldFNob3dVc2VyTW9kYWwoZmFsc2UpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGNvbXByYXIgdXN1w6FyaW9zOicsIGVycm9yKTtcbiAgICAgIGFkZFRvYXN0KGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICdFcnJvIGFvIGNvbXByYXIgdXN1w6FyaW9zJywgJ2Vycm9yJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldEFjdGlvbkxvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRTdGF0dXNDb2xvciA9IChzdGF0dXMpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnQUNUSVZFJzogcmV0dXJuICd0ZXh0LWdyZWVuLTYwMCBiZy1ncmVlbi0xMDAnO1xuICAgICAgY2FzZSAnVFJJQUwnOiByZXR1cm4gJ3RleHQtYmx1ZS02MDAgYmctYmx1ZS0xMDAnO1xuICAgICAgY2FzZSAnUEFTVF9EVUUnOiByZXR1cm4gJ3RleHQteWVsbG93LTYwMCBiZy15ZWxsb3ctMTAwJztcbiAgICAgIGNhc2UgJ0NBTkNFTEVEJzogcmV0dXJuICd0ZXh0LXJlZC02MDAgYmctcmVkLTEwMCc7XG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ3RleHQtZ3JheS02MDAgYmctZ3JheS0xMDAnO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRTdGF0dXNUZXh0ID0gKHN0YXR1cykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdBQ1RJVkUnOiByZXR1cm4gJ0F0aXZvJztcbiAgICAgIGNhc2UgJ1RSSUFMJzogcmV0dXJuICdQZXLDrW9kbyBkZSBUZXN0ZSc7XG4gICAgICBjYXNlICdQQVNUX0RVRSc6IHJldHVybiAnUGFnYW1lbnRvIGVtIEF0cmFzbyc7XG4gICAgICBjYXNlICdDQU5DRUxFRCc6IHJldHVybiAnQ2FuY2VsYWRvJztcbiAgICAgIGRlZmF1bHQ6IHJldHVybiBzdGF0dXM7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGZvcm1hdERhdGUgPSAoZGF0ZVN0cmluZykgPT4ge1xuICAgIHJldHVybiBuZXcgRGF0ZShkYXRlU3RyaW5nKS50b0xvY2FsZURhdGVTdHJpbmcoJ3B0LUJSJyk7XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0Q3VycmVuY3kgPSAodmFsdWUpID0+IHtcbiAgICByZXR1cm4gbmV3IEludGwuTnVtYmVyRm9ybWF0KCdwdC1CUicsIHtcbiAgICAgIHN0eWxlOiAnY3VycmVuY3knLFxuICAgICAgY3VycmVuY3k6ICdCUkwnXG4gICAgfSkuZm9ybWF0KHZhbHVlKTtcbiAgfTtcblxuICBpZiAoaXNMb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC02NFwiPlxuICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJoLTggdy04IGFuaW1hdGUtc3BpbiB0ZXh0LWFkbWluLXByaW1hcnlcIiAvPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIC8vIFNlIGZvciBTWVNURU1fQURNSU4sIG1vc3RyYXIgc2VsZXRvciBkZSBlbXByZXNhXG4gIGlmICh1c2VyPy5yb2xlID09PSAnU1lTVEVNX0FETUlOJykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICA8TW9kdWxlSGVhZGVyXG4gICAgICAgICAgdGl0bGU9XCJHZXJlbmNpYW1lbnRvIGRlIFBsYW5vc1wiXG4gICAgICAgICAgZGVzY3JpcHRpb249XCJTZWxlY2lvbmUgdW1hIGVtcHJlc2EgcGFyYSBnZXJlbmNpYXIgc3VhIGFzc2luYXR1cmEsIG3Ds2R1bG9zIGUgZmF0dXJhbWVudG9cIlxuICAgICAgICAgIGljb249ezxDcmVkaXRDYXJkIHNpemU9ezIwfSAvPn1cbiAgICAgICAgICBtb2R1bGVDb2xvcj1cImFkbWluXCJcbiAgICAgICAgLz5cblxuICAgICAgICA8TW9kdWxlQ2FyZCBtb2R1bGVDb2xvcj1cImFkbWluXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGUgbWItNFwiPlxuICAgICAgICAgICAgICAgIFNlbGVjaW9uYXIgRW1wcmVzYVxuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICA8TW9kdWxlU2VsZWN0XG4gICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkQ29tcGFueUlkIHx8ICcnfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VsZWN0ZWRDb21wYW55SWQoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIG1vZHVsZUNvbG9yPVwiYWRtaW5cIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBtYXgtdy1tZFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+U2VsZWNpb25lIHVtYSBlbXByZXNhLi4uPC9vcHRpb24+XG4gICAgICAgICAgICAgICAge2NvbXBhbmllcy5tYXAoKGNvbXBhbnkpID0+IChcbiAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXtjb21wYW55LmlkfSB2YWx1ZT17Y29tcGFueS5pZH0+XG4gICAgICAgICAgICAgICAgICAgIHtjb21wYW55Lm5hbWV9XG4gICAgICAgICAgICAgICAgICA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9Nb2R1bGVTZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgeyFzZWxlY3RlZENvbXBhbnlJZCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byB3LTEyIGgtMTIgYmctYWRtaW4tcHJpbWFyeS8xMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1hZG1pbi1wcmltYXJ5XCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgU2VsZWNpb25lIHVtYSBlbXByZXNhIGFjaW1hIHBhcmEgdmlzdWFsaXphciBlIGdlcmVuY2lhciBzZXVzIHBsYW5vcyBkZSBhc3NpbmF0dXJhLlxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7c2VsZWN0ZWRDb21wYW55SWQgJiYgIXN1YnNjcmlwdGlvbiAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byB3LTEyIGgtMTIgYmcteWVsbG93LTEwMCBkYXJrOmJnLXllbGxvdy05MDAvMjAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQteWVsbG93LTYwMCBkYXJrOnRleHQteWVsbG93LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZSBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICBOZW5odW1hIEFzc2luYXR1cmEgRW5jb250cmFkYVxuICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgIEEgZW1wcmVzYSBzZWxlY2lvbmFkYSBuw6NvIHBvc3N1aSB1bWEgYXNzaW5hdHVyYSBhdGl2YSBubyBtb21lbnRvLlxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L01vZHVsZUNhcmQ+XG5cbiAgICAgICAgey8qIFNlIGjDoSBhc3NpbmF0dXJhIHNlbGVjaW9uYWRhLCBtb3N0cmFyIG8gY29udGXDumRvIG5vcm1hbCAqL31cbiAgICAgICAge3NlbGVjdGVkQ29tcGFueUlkICYmIHN1YnNjcmlwdGlvbiAmJiAoXG4gICAgICAgICAgPD5cbiAgICAgICAgICAgIHsvKiBTdGF0dXMgZGEgQXNzaW5hdHVyYSBwYXJhIFNZU1RFTV9BRE1JTiAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtNCBnYXAtNlwiPlxuICAgICAgICAgICAgICB7LyogQ2FyZCBQcmluY2lwYWwgZGEgQXNzaW5hdHVyYSAqL31cbiAgICAgICAgICAgICAgPE1vZHVsZUNhcmQgbW9kdWxlQ29sb3I9XCJhZG1pblwiIGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1hZG1pbi1wcmltYXJ5LzEwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q3Jvd24gY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWFkbWluLXByaW1hcnlcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBQbGFubyBkYSBFbXByZXNhXG4gICAgICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7c3Vic2NyaXB0aW9uLmJpbGxpbmdDeWNsZSA9PT0gJ1lFQVJMWScgPyAnRmF0dXJhbWVudG8gQW51YWwnIDogJ0ZhdHVyYW1lbnRvIE1lbnNhbCd9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgcHgtMyBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXNtIGZvbnQtbWVkaXVtICR7Z2V0U3RhdHVzQ29sb3Ioc3Vic2NyaXB0aW9uLnN0YXR1cyl9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtnZXRTdGF0dXNUZXh0KHN1YnNjcmlwdGlvbi5zdGF0dXMpfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgbWItMVwiPlZhbG9yIE1lbnNhbDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRDdXJyZW5jeShzdWJzY3JpcHRpb24ucHJpY2VQZXJNb250aCl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwIG1iLTFcIj5QcsOzeGltbyBQYWdhbWVudG88L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtzdWJzY3JpcHRpb24ubmV4dEJpbGxpbmdEYXRlID8gZm9ybWF0RGF0ZShzdWJzY3JpcHRpb24ubmV4dEJpbGxpbmdEYXRlKSA6ICdOL0EnfVxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMCBtYi0xXCI+RGF0YSBkZSBJbsOtY2lvPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZShzdWJzY3JpcHRpb24uc3RhcnREYXRlKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7c3Vic2NyaXB0aW9uLmNhbmNlbEF0UGVyaW9kRW5kICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiBwLTQgYmcteWVsbG93LTUwIGRhcms6YmcteWVsbG93LTkwMC8yMCBib3JkZXIgYm9yZGVyLXllbGxvdy0yMDAgZGFyazpib3JkZXIteWVsbG93LTgwMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXllbGxvdy02MDAgZGFyazp0ZXh0LXllbGxvdy00MDAgbXItM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC15ZWxsb3ctODAwIGRhcms6dGV4dC15ZWxsb3ctMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIENhbmNlbGFtZW50byBBZ2VuZGFkb1xuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXllbGxvdy03MDAgZGFyazp0ZXh0LXllbGxvdy0zMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgQSBhc3NpbmF0dXJhIGRlc3RhIGVtcHJlc2Egc2Vyw6EgY2FuY2VsYWRhIG5vIGZpbmFsIGRvIHBlcsOtb2RvIGF0dWFsLlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvTW9kdWxlQ2FyZD5cblxuICAgICAgICAgICAgICB7LyogQ2FyZCBkZSBVc3XDoXJpb3MgKi99XG4gICAgICAgICAgICAgIDxNb2R1bGVDYXJkIG1vZHVsZUNvbG9yPVwiYWRtaW5cIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm14LWF1dG8gdy0xMiBoLTEyIGJnLWJsdWUtMTAwIGRhcms6YmctYmx1ZS05MDAvMjAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPFVzZXJzIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgVXN1w6FyaW9zXG4gICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNjAwIGRhcms6dGV4dC1ibHVlLTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHt1c2VyU3RhdHMuY3VycmVudH1cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgZGUge3VzZXJTdGF0cy5saW1pdH0gZGlzcG9uw612ZWlzXG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS0yMDAgZGFyazpiZy1ncmF5LTcwMCByb3VuZGVkLWZ1bGwgaC0yIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIGgtMiByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7TWF0aC5taW4oKHVzZXJTdGF0cy5jdXJyZW50IC8gdXNlclN0YXRzLmxpbWl0KSAqIDEwMCwgMTAwKX0lYCB9fVxuICAgICAgICAgICAgICAgICAgICA+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9Nb2R1bGVDYXJkPlxuXG4gICAgICAgICAgICAgIHsvKiBDYXJkIGRlIE3Ds2R1bG9zICovfVxuICAgICAgICAgICAgICA8TW9kdWxlQ2FyZCBtb2R1bGVDb2xvcj1cImFkbWluXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteC1hdXRvIHctMTIgaC0xMiBiZy1ncmVlbi0xMDAgZGFyazpiZy1ncmVlbi05MDAvMjAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPFBhY2thZ2UgY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LWdyZWVuLTYwMCBkYXJrOnRleHQtZ3JlZW4tNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZSBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgIE3Ds2R1bG9zXG4gICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JlZW4tNjAwIGRhcms6dGV4dC1ncmVlbi00MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICB7c3Vic2NyaXB0aW9uLm1vZHVsZXM/LmZpbHRlcihtID0+IG0uYWN0aXZlKS5sZW5ndGggfHwgMH1cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDxNb2R1bGVCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93TW9kdWxlTW9kYWwodHJ1ZSl9XG4gICAgICAgICAgICAgICAgICAgIG1vZHVsZUNvbG9yPVwiYWRtaW5cIlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8U2V0dGluZ3MgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgR2VyZW5jaWFyXG4gICAgICAgICAgICAgICAgICA8L01vZHVsZUJ1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9Nb2R1bGVDYXJkPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC8+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgPE1vZHVsZUhlYWRlclxuICAgICAgICB0aXRsZT1cIkdlcmVuY2lhbWVudG8gZGUgUGxhbm9zXCJcbiAgICAgICAgZGVzY3JpcHRpb249XCJHZXJlbmNpZSBzdWEgYXNzaW5hdHVyYSwgbcOzZHVsb3MgZSBmYXR1cmFtZW50b1wiXG4gICAgICAgIGljb249ezxDcmVkaXRDYXJkIHNpemU9ezIwfSAvPn1cbiAgICAgICAgbW9kdWxlQ29sb3I9XCJhZG1pblwiXG4gICAgICAvPlxuXG4gICAgICB7LyogU3RhdHVzIGRhIEFzc2luYXR1cmEgKi99XG4gICAgICB7c3Vic2NyaXB0aW9uICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy00IGdhcC02XCI+XG4gICAgICAgICAgey8qIENhcmQgUHJpbmNpcGFsIGRhIEFzc2luYXR1cmEgKi99XG4gICAgICAgICAgPE1vZHVsZUNhcmQgbW9kdWxlQ29sb3I9XCJhZG1pblwiIGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1hZG1pbi1wcmltYXJ5LzEwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxDcm93biBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtYWRtaW4tcHJpbWFyeVwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgUGxhbm8gQXR1YWxcbiAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtzdWJzY3JpcHRpb24uYmlsbGluZ0N5Y2xlID09PSAnWUVBUkxZJyA/ICdGYXR1cmFtZW50byBBbnVhbCcgOiAnRmF0dXJhbWVudG8gTWVuc2FsJ31cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHB4LTMgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC1zbSBmb250LW1lZGl1bSAke2dldFN0YXR1c0NvbG9yKHN1YnNjcmlwdGlvbi5zdGF0dXMpfWB9PlxuICAgICAgICAgICAgICAgIHtnZXRTdGF0dXNUZXh0KHN1YnNjcmlwdGlvbi5zdGF0dXMpfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgbWItMVwiPlZhbG9yIE1lbnNhbDwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0Q3VycmVuY3koc3Vic2NyaXB0aW9uLnByaWNlUGVyTW9udGgpfVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwIG1iLTFcIj5QcsOzeGltbyBQYWdhbWVudG88L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICB7c3Vic2NyaXB0aW9uLm5leHRCaWxsaW5nRGF0ZSA/IGZvcm1hdERhdGUoc3Vic2NyaXB0aW9uLm5leHRCaWxsaW5nRGF0ZSkgOiAnTi9BJ31cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgbWItMVwiPkRhdGEgZGUgSW7DrWNpbzwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXREYXRlKHN1YnNjcmlwdGlvbi5zdGFydERhdGUpfVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8TW9kdWxlQnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1VwZ3JhZGVNb2RhbCh0cnVlKX1cbiAgICAgICAgICAgICAgICAgICAgbW9kdWxlQ29sb3I9XCJhZG1pblwiXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxBcnJvd1VwQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgIEZhemVyIFVwZ3JhZGVcbiAgICAgICAgICAgICAgICAgIDwvTW9kdWxlQnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7c3Vic2NyaXB0aW9uLmNhbmNlbEF0UGVyaW9kRW5kICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02IHAtNCBiZy15ZWxsb3ctNTAgZGFyazpiZy15ZWxsb3ctOTAwLzIwIGJvcmRlciBib3JkZXIteWVsbG93LTIwMCBkYXJrOmJvcmRlci15ZWxsb3ctODAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXllbGxvdy02MDAgZGFyazp0ZXh0LXllbGxvdy00MDAgbXItM1wiIC8+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQteWVsbG93LTgwMCBkYXJrOnRleHQteWVsbG93LTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIENhbmNlbGFtZW50byBBZ2VuZGFkb1xuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC15ZWxsb3ctNzAwIGRhcms6dGV4dC15ZWxsb3ctMzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgU3VhIGFzc2luYXR1cmEgc2Vyw6EgY2FuY2VsYWRhIG5vIGZpbmFsIGRvIHBlcsOtb2RvIGF0dWFsLlxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvTW9kdWxlQ2FyZD5cblxuICAgICAgICAgIHsvKiBDYXJkIGRlIFVzdcOhcmlvcyAqL31cbiAgICAgICAgICA8TW9kdWxlQ2FyZCBtb2R1bGVDb2xvcj1cImFkbWluXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byB3LTEyIGgtMTIgYmctYmx1ZS0xMDAgZGFyazpiZy1ibHVlLTkwMC8yMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgICAgICAgIDxVc2VycyBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtYmx1ZS02MDAgZGFyazp0ZXh0LWJsdWUtNDAwXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGUgbWItMlwiPlxuICAgICAgICAgICAgICAgIFVzdcOhcmlvc1xuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDBcIj5cbiAgICAgICAgICAgICAgICAgIHt1c2VyU3RhdHMuY3VycmVudH1cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgZGUge3VzZXJTdGF0cy5saW1pdH0gZGlzcG9uw612ZWlzXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS0yMDAgZGFyazpiZy1ncmF5LTcwMCByb3VuZGVkLWZ1bGwgaC0yIG1iLTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTYwMCBoLTIgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7TWF0aC5taW4oKHVzZXJTdGF0cy5jdXJyZW50IC8gdXNlclN0YXRzLmxpbWl0KSAqIDEwMCwgMTAwKX0lYCB9fVxuICAgICAgICAgICAgICAgID48L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxNb2R1bGVCdXR0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93VXNlck1vZGFsKHRydWUpfVxuICAgICAgICAgICAgICAgIG1vZHVsZUNvbG9yPVwiYWRtaW5cIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8VXNlclBsdXMgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICBDb21wcmFyIFVzdcOhcmlvc1xuICAgICAgICAgICAgICA8L01vZHVsZUJ1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvTW9kdWxlQ2FyZD5cblxuICAgICAgICAgIHsvKiBDYXJkIGRlIE3Ds2R1bG9zICovfVxuICAgICAgICAgIDxNb2R1bGVDYXJkIG1vZHVsZUNvbG9yPVwiYWRtaW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteC1hdXRvIHctMTIgaC0xMiBiZy1ncmVlbi0xMDAgZGFyazpiZy1ncmVlbi05MDAvMjAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1iLTRcIj5cbiAgICAgICAgICAgICAgICA8UGFja2FnZSBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtZ3JlZW4tNjAwIGRhcms6dGV4dC1ncmVlbi00MDBcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZSBtYi0yXCI+XG4gICAgICAgICAgICAgICAgTcOzZHVsb3NcbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JlZW4tNjAwIGRhcms6dGV4dC1ncmVlbi00MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgIHtzdWJzY3JpcHRpb24ubW9kdWxlcz8uZmlsdGVyKG0gPT4gbS5hY3RpdmUpLmxlbmd0aCB8fCAwfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDxNb2R1bGVCdXR0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93TW9kdWxlTW9kYWwodHJ1ZSl9XG4gICAgICAgICAgICAgICAgbW9kdWxlQ29sb3I9XCJhZG1pblwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxTZXR0aW5ncyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIEdlcmVuY2lhclxuICAgICAgICAgICAgICA8L01vZHVsZUJ1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvTW9kdWxlQ2FyZD5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogTcOzZHVsb3MgKi99XG4gICAgICB7c3Vic2NyaXB0aW9uPy5tb2R1bGVzICYmIChcbiAgICAgICAgPE1vZHVsZUNhcmQgbW9kdWxlQ29sb3I9XCJhZG1pblwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTZcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgTcOzZHVsb3MgZGEgQXNzaW5hdHVyYVxuICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgIDxNb2R1bGVCdXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd01vZHVsZU1vZGFsKHRydWUpfVxuICAgICAgICAgICAgICBtb2R1bGVDb2xvcj1cImFkbWluXCJcbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgQWRpY2lvbmFyIE3Ds2R1bG9cbiAgICAgICAgICAgIDwvTW9kdWxlQnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC00XCI+XG4gICAgICAgICAgICB7c3Vic2NyaXB0aW9uLm1vZHVsZXMubWFwKChtb2R1bGUpID0+IChcbiAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgIGtleT17bW9kdWxlLmlkfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtNCBib3JkZXIgcm91bmRlZC1sZyAke1xuICAgICAgICAgICAgICAgICAgbW9kdWxlLmFjdGl2ZVxuICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItZ3JlZW4tMjAwIGJnLWdyZWVuLTUwIGRhcms6Ym9yZGVyLWdyZWVuLTgwMCBkYXJrOmJnLWdyZWVuLTkwMC8yMCdcbiAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLWdyYXktMjAwIGJnLWdyYXktNTAgZGFyazpib3JkZXItZ3JheS03MDAgZGFyazpiZy1ncmF5LTgwMCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICB7cGxhbnM/Lm1vZHVsZXM/Llttb2R1bGUubW9kdWxlVHlwZV0/Lm5hbWUgfHwgbW9kdWxlLm1vZHVsZVR5cGV9XG4gICAgICAgICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICAgICAgICAge21vZHVsZS5hY3RpdmUgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDxYQ2lyY2xlIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgbWItM1wiPlxuICAgICAgICAgICAgICAgICAge2Zvcm1hdEN1cnJlbmN5KG1vZHVsZS5wcmljZVBlck1vbnRoKX0vbcOqc1xuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICB7bW9kdWxlLmFjdGl2ZSAmJiAhWydCQVNJQycsICdBRE1JTicsICdTQ0hFRFVMSU5HJ10uaW5jbHVkZXMobW9kdWxlLm1vZHVsZVR5cGUpICYmIChcbiAgICAgICAgICAgICAgICAgIDxNb2R1bGVCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVSZW1vdmVNb2R1bGUobW9kdWxlLm1vZHVsZVR5cGUpfVxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17YWN0aW9uTG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtcmVkLTYwMCBib3JkZXItcmVkLTIwMCBob3ZlcjpiZy1yZWQtNTBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8TWludXMgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgUmVtb3ZlclxuICAgICAgICAgICAgICAgICAgPC9Nb2R1bGVCdXR0b24+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9Nb2R1bGVDYXJkPlxuICAgICAgKX1cblxuICAgICAgey8qIEhpc3TDs3JpY28gZGUgRmF0dXJhcyAqL31cbiAgICAgIHtpbnZvaWNlcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgPE1vZHVsZUNhcmQgbW9kdWxlQ29sb3I9XCJhZG1pblwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTZcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgSGlzdMOzcmljbyBkZSBGYXR1cmFzXG4gICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgPE1vZHVsZUJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2xvYWREYXRhfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgICBtb2R1bGVDb2xvcj1cImFkbWluXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9e2BoLTQgdy00IG1yLTIgJHtpc0xvYWRpbmcgPyAnYW5pbWF0ZS1zcGluJyA6ICcnfWB9IC8+XG4gICAgICAgICAgICAgIEF0dWFsaXphclxuICAgICAgICAgICAgPC9Nb2R1bGVCdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICAgICAgPHRhYmxlIGNsYXNzTmFtZT1cInctZnVsbFwiPlxuICAgICAgICAgICAgICA8dGhlYWQ+XG4gICAgICAgICAgICAgICAgPHRyIGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtbGVmdCBweS0zIHB4LTQgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgRGF0YVxuICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LWxlZnQgcHktMyBweC00IGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgIFZhbG9yXG4gICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtbGVmdCBweS0zIHB4LTQgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgU3RhdHVzXG4gICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtbGVmdCBweS0zIHB4LTQgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgVmVuY2ltZW50b1xuICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0IHB5LTMgcHgtNCBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICBBw6fDtWVzXG4gICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICAgICAgIDx0Ym9keT5cbiAgICAgICAgICAgICAgICB7aW52b2ljZXMubWFwKChpbnZvaWNlKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8dHIga2V5PXtpbnZvaWNlLmlkfSBjbGFzc05hbWU9XCJib3JkZXItYiBib3JkZXItZ3JheS0xMDAgZGFyazpib3JkZXItZ3JheS04MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXREYXRlKGludm9pY2UuY3JlYXRlZEF0KX1cbiAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRDdXJyZW5jeShpbnZvaWNlLmFtb3VudCl9XG4gICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BweC0yIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGludm9pY2Uuc3RhdHVzID09PSAnUEFJRCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyAndGV4dC1ncmVlbi02MDAgYmctZ3JlZW4tMTAwIGRhcms6dGV4dC1ncmVlbi00MDAgZGFyazpiZy1ncmVlbi05MDAvMjAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogaW52b2ljZS5zdGF0dXMgPT09ICdQRU5ESU5HJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICA/ICd0ZXh0LXllbGxvdy02MDAgYmcteWVsbG93LTEwMCBkYXJrOnRleHQteWVsbG93LTQwMCBkYXJrOmJnLXllbGxvdy05MDAvMjAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtcmVkLTYwMCBiZy1yZWQtMTAwIGRhcms6dGV4dC1yZWQtNDAwIGRhcms6YmctcmVkLTkwMC8yMCdcbiAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7aW52b2ljZS5zdGF0dXMgPT09ICdQQUlEJyA/ICdQYWdvJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgaW52b2ljZS5zdGF0dXMgPT09ICdQRU5ESU5HJyA/ICdQZW5kZW50ZScgOiAnRmFsaG91J31cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS0zIHB4LTQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZShpbnZvaWNlLmR1ZURhdGUpfVxuICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00IHRleHQtcmlnaHRcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7aW52b2ljZS5zdHJpcGVJbnZvaWNlVXJsICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2ludm9pY2Uuc3RyaXBlSW52b2ljZVVybH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgdGFyZ2V0PVwiX2JsYW5rXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LWFkbWluLXByaW1hcnkgaG92ZXI6dGV4dC1hZG1pbi1wcmltYXJ5LzgwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPERvd25sb2FkIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIERvd25sb2FkXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvdGJvZHk+XG4gICAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L01vZHVsZUNhcmQ+XG4gICAgICApfVxuXG4gICAgICB7LyogQcOnw7VlcyBSw6FwaWRhcyAqL31cbiAgICAgIHtzdWJzY3JpcHRpb24gJiYgc3Vic2NyaXB0aW9uLnN0YXR1cyA9PT0gJ0FDVElWRScgJiYgKFxuICAgICAgICA8TW9kdWxlQ2FyZCBtb2R1bGVDb2xvcj1cImFkbWluXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZSBtYi0yXCI+XG4gICAgICAgICAgICAgICAgQcOnw7VlcyBSw6FwaWRhc1xuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgIEdlcmVuY2llIHN1YSBhc3NpbmF0dXJhIGUgcmVjdXJzb3NcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTRcIj5cbiAgICAgICAgICAgIHsvKiBVcGdyYWRlIGRlIFBsYW5vICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMCByb3VuZGVkLWxnIGhvdmVyOmJvcmRlci1hZG1pbi1wcmltYXJ5LzMwIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIG1iLTNcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctYWRtaW4tcHJpbWFyeS8xMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8VHJlbmRpbmdVcCBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtYWRtaW4tcHJpbWFyeVwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+VXBncmFkZSBkZSBQbGFubzwvaDQ+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwIG1iLTRcIj5cbiAgICAgICAgICAgICAgICBBY2Vzc2UgcmVjdXJzb3MgYXZhbsOnYWRvcyBlIGF1bWVudGUgc3VhIGNhcGFjaWRhZGVcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8TW9kdWxlQnV0dG9uXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1VwZ3JhZGVNb2RhbCh0cnVlKX1cbiAgICAgICAgICAgICAgICBtb2R1bGVDb2xvcj1cImFkbWluXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPEFycm93VXBDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICBGYXplciBVcGdyYWRlXG4gICAgICAgICAgICAgIDwvTW9kdWxlQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBDb21wcmFyIFVzdcOhcmlvcyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJvcmRlciBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS03MDAgcm91bmRlZC1sZyBob3Zlcjpib3JkZXItYmx1ZS0zMDAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgbWItM1wiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ibHVlLTEwMCBkYXJrOmJnLWJsdWUtOTAwLzIwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxVc2VyUGx1cyBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtYmx1ZS02MDAgZGFyazp0ZXh0LWJsdWUtNDAwXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5NYWlzIFVzdcOhcmlvczwvaDQ+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwIG1iLTRcIj5cbiAgICAgICAgICAgICAgICBBZGljaW9uZSBtYWlzIHVzdcOhcmlvcyBhbyBzZXUgcGxhbm8gYXR1YWxcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8TW9kdWxlQnV0dG9uXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1VzZXJNb2RhbCh0cnVlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYm9yZGVyLWJsdWUtMjAwIHRleHQtYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS01MCBkYXJrOmJvcmRlci1ibHVlLTgwMCBkYXJrOnRleHQtYmx1ZS00MDAgZGFyazpob3ZlcjpiZy1ibHVlLTkwMC8yMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIENvbXByYXIgVXN1w6FyaW9zXG4gICAgICAgICAgICAgIDwvTW9kdWxlQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBDYW5jZWxhciBBc3NpbmF0dXJhICovfVxuICAgICAgICAgICAgeyFzdWJzY3JpcHRpb24uY2FuY2VsQXRQZXJpb2RFbmQgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIGRhcms6Ym9yZGVyLWdyYXktNzAwIHJvdW5kZWQtbGcgaG92ZXI6Ym9yZGVyLXJlZC0zMDAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBtYi0zXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctcmVkLTEwMCBkYXJrOmJnLXJlZC05MDAvMjAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8VHJhc2gyIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1yZWQtNjAwIGRhcms6dGV4dC1yZWQtNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+Q2FuY2VsYXIgUGxhbm88L2g0PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgQ2FuY2VsZSBzdWEgYXNzaW5hdHVyYSBhIHF1YWxxdWVyIG1vbWVudG9cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPE1vZHVsZUJ1dHRvblxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dDYW5jZWxNb2RhbCh0cnVlKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LXJlZC02MDAgYm9yZGVyLXJlZC0yMDAgaG92ZXI6YmctcmVkLTUwIGRhcms6dGV4dC1yZWQtNDAwIGRhcms6Ym9yZGVyLXJlZC04MDAgZGFyazpob3ZlcjpiZy1yZWQtOTAwLzIwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8VHJhc2gyIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICBDYW5jZWxhclxuICAgICAgICAgICAgICAgIDwvTW9kdWxlQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvTW9kdWxlQ2FyZD5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBNb2RhbCBkZSBDYW5jZWxhbWVudG8gKi99XG4gICAgICA8TW9kdWxlTW9kYWxcbiAgICAgICAgaXNPcGVuPXtzaG93Q2FuY2VsTW9kYWx9XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldFNob3dDYW5jZWxNb2RhbChmYWxzZSl9XG4gICAgICAgIHRpdGxlPVwiQ2FuY2VsYXIgQXNzaW5hdHVyYVwiXG4gICAgICAgIG1vZHVsZUNvbG9yPVwiYWRtaW5cIlxuICAgICAgPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcC00IGJnLXllbGxvdy01MCBkYXJrOmJnLXllbGxvdy05MDAvMjAgYm9yZGVyIGJvcmRlci15ZWxsb3ctMjAwIGRhcms6Ym9yZGVyLXllbGxvdy04MDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC15ZWxsb3ctNjAwIGRhcms6dGV4dC15ZWxsb3ctNDAwIG1yLTNcIiAvPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXllbGxvdy04MDAgZGFyazp0ZXh0LXllbGxvdy0yMDBcIj5cbiAgICAgICAgICAgICAgICBBdGVuw6fDo28hXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXllbGxvdy03MDAgZGFyazp0ZXh0LXllbGxvdy0zMDBcIj5cbiAgICAgICAgICAgICAgICBBbyBjYW5jZWxhciwgdm9jw6ogcGVyZGVyw6EgYWNlc3NvIGFvcyByZWN1cnNvcyBwYWdvcyBubyBmaW5hbCBkbyBwZXLDrW9kbyBhdHVhbC5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgVGVtIGNlcnRlemEgZGUgcXVlIGRlc2VqYSBjYW5jZWxhciBzdWEgYXNzaW5hdHVyYT8gRXN0YSBhw6fDo28gbsOjbyBwb2RlIHNlciBkZXNmZWl0YS5cbiAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICA8TW9kdWxlQnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0NhbmNlbE1vZGFsKGZhbHNlKX1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2FjdGlvbkxvYWRpbmd9XG4gICAgICAgICAgICAgIG1vZHVsZUNvbG9yPVwiYWRtaW5cIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBNYW50ZXIgQXNzaW5hdHVyYVxuICAgICAgICAgICAgPC9Nb2R1bGVCdXR0b24+XG4gICAgICAgICAgICA8TW9kdWxlQnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNhbmNlbFN1YnNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2FjdGlvbkxvYWRpbmd9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXJlZC02MDAgaG92ZXI6YmctcmVkLTcwMCB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2FjdGlvbkxvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwiaC00IHctNCBhbmltYXRlLXNwaW4gbXItMlwiIC8+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPFRyYXNoMiBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICBDYW5jZWxhciBBc3NpbmF0dXJhXG4gICAgICAgICAgICA8L01vZHVsZUJ1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L01vZHVsZU1vZGFsPlxuXG4gICAgICB7LyogTW9kYWwgZGUgTcOzZHVsb3MgKi99XG4gICAgICA8TW9kdWxlTW9kYWxcbiAgICAgICAgaXNPcGVuPXtzaG93TW9kdWxlTW9kYWx9XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldFNob3dNb2R1bGVNb2RhbChmYWxzZSl9XG4gICAgICAgIHRpdGxlPVwiR2VyZW5jaWFyIE3Ds2R1bG9zXCJcbiAgICAgICAgbW9kdWxlQ29sb3I9XCJhZG1pblwiXG4gICAgICAgIHNpemU9XCJsZ1wiXG4gICAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgIEFkaWNpb25lIG91IHJlbW92YSBtw7NkdWxvcyBkYSBzdWEgYXNzaW5hdHVyYS4gT3MgbcOzZHVsb3MgYsOhc2ljb3MgbsOjbyBwb2RlbSBzZXIgcmVtb3ZpZG9zLlxuICAgICAgICAgIDwvcD5cblxuICAgICAgICAgIHtwbGFucz8ubW9kdWxlcyAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAge09iamVjdC5lbnRyaWVzKHBsYW5zLm1vZHVsZXMpLm1hcCgoW21vZHVsZVR5cGUsIG1vZHVsZUluZm9dKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgaXNBY3RpdmUgPSBzdWJzY3JpcHRpb24/Lm1vZHVsZXM/LmZpbmQobSA9PiBtLm1vZHVsZVR5cGUgPT09IG1vZHVsZVR5cGUgJiYgbS5hY3RpdmUpO1xuICAgICAgICAgICAgICAgIGNvbnN0IGlzQmFzaWMgPSBbJ0JBU0lDJywgJ0FETUlOJywgJ1NDSEVEVUxJTkcnXS5pbmNsdWRlcyhtb2R1bGVUeXBlKTtcblxuICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgIGtleT17bW9kdWxlVHlwZX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC00IGJvcmRlciByb3VuZGVkLWxnICR7XG4gICAgICAgICAgICAgICAgICAgICAgaXNBY3RpdmVcbiAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1ncmVlbi0yMDAgYmctZ3JlZW4tNTAgZGFyazpib3JkZXItZ3JlZW4tODAwIGRhcms6YmctZ3JlZW4tOTAwLzIwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLWdyYXktMjAwIGJnLXdoaXRlIGRhcms6Ym9yZGVyLWdyYXktNzAwIGRhcms6YmctZ3JheS04MDAnXG4gICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7bW9kdWxlSW5mby5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAge2lzQWN0aXZlICYmIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JlZW4tNjAwXCIgLz59XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgbWItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIHttb2R1bGVJbmZvLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0Q3VycmVuY3kobW9kdWxlSW5mby5tb250aGx5UHJpY2UpfS9tw6pzXG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuXG4gICAgICAgICAgICAgICAgICAgICAgeyFpc0Jhc2ljICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxNb2R1bGVCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD17aXNBY3RpdmUgPyBcIm91dGxpbmVcIiA6IFwiZGVmYXVsdFwifVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBpc0FjdGl2ZSA/IGhhbmRsZVJlbW92ZU1vZHVsZShtb2R1bGVUeXBlKSA6IGhhbmRsZUFkZE1vZHVsZShtb2R1bGVUeXBlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2FjdGlvbkxvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG1vZHVsZUNvbG9yPVwiYWRtaW5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2lzQWN0aXZlID8gXCJ0ZXh0LXJlZC02MDAgYm9yZGVyLXJlZC0yMDAgaG92ZXI6YmctcmVkLTUwXCIgOiBcIlwifVxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7YWN0aW9uTG9hZGluZyA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJoLTQgdy00IGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICkgOiBpc0FjdGl2ZSA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPE1pbnVzIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBSZW1vdmVyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBBZGljaW9uYXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTW9kdWxlQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgICAgICB7aXNCYXNpYyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIEluY2x1w61kb1xuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvTW9kdWxlTW9kYWw+XG5cbiAgICAgIHsvKiBNb2RhbCBkZSBVcGdyYWRlICovfVxuICAgICAgPE1vZHVsZU1vZGFsXG4gICAgICAgIGlzT3Blbj17c2hvd1VwZ3JhZGVNb2RhbH1cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd1VwZ3JhZGVNb2RhbChmYWxzZSl9XG4gICAgICAgIHRpdGxlPVwiVXBncmFkZSBkZSBQbGFub1wiXG4gICAgICAgIG1vZHVsZUNvbG9yPVwiYWRtaW5cIlxuICAgICAgICBzaXplPVwibGdcIlxuICAgICAgPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byB3LTE2IGgtMTYgYmctYWRtaW4tcHJpbWFyeS8xMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgICAgICA8Q3Jvd24gY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LWFkbWluLXByaW1hcnlcIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlIG1iLTJcIj5cbiAgICAgICAgICAgICAgRmHDp2EgVXBncmFkZSBkbyBTZXUgUGxhbm9cbiAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICBEZXNibG9xdWVpZSByZWN1cnNvcyBhdmFuw6dhZG9zIGUgYXVtZW50ZSBzdWEgY2FwYWNpZGFkZVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBib3JkZXItMiBib3JkZXItYWRtaW4tcHJpbWFyeS8yMCByb3VuZGVkLWxnIGJnLWFkbWluLXByaW1hcnkvNVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBtYi00XCI+XG4gICAgICAgICAgICAgICAgPFNoaWVsZCBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtYWRtaW4tcHJpbWFyeVwiIC8+XG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5QbGFubyBQcm9maXNzaW9uYWw8L2g0PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInNwYWNlLXktMiB0ZXh0LXNtIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwIG1iLTZcIj5cbiAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JlZW4tNjAwIG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgVXN1w6FyaW9zIGlsaW1pdGFkb3NcbiAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmVlbi02MDAgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICBUb2RvcyBvcyBtw7NkdWxvcyBpbmNsdXNvc1xuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyZWVuLTYwMCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgIFN1cG9ydGUgcHJpb3JpdMOhcmlvXG4gICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JlZW4tNjAwIG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgUmVsYXTDs3Jpb3MgYXZhbsOnYWRvc1xuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1hZG1pbi1wcmltYXJ5IG1iLTJcIj5SJCAyOTksOTA8L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPnBvciBtw6pzPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBib3JkZXItMiBib3JkZXItcHVycGxlLTIwMCByb3VuZGVkLWxnIGJnLXB1cnBsZS01MCBkYXJrOmJnLXB1cnBsZS05MDAvMTBcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgbWItNFwiPlxuICAgICAgICAgICAgICAgIDxaYXAgY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LXB1cnBsZS02MDBcIiAvPlxuICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+UGxhbm8gRW50ZXJwcmlzZTwvaDQ+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yIHRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgbWItNlwiPlxuICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmVlbi02MDAgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICBUdWRvIGRvIFByb2Zpc3Npb25hbFxuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyZWVuLTYwMCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgIEFQSSBwZXJzb25hbGl6YWRhXG4gICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JlZW4tNjAwIG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgSW50ZWdyYcOnw6NvIGRlZGljYWRhXG4gICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JlZW4tNjAwIG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgR2VyZW50ZSBkZSBjb250YVxuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wdXJwbGUtNjAwIG1iLTJcIj5SJCA1OTksOTA8L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPnBvciBtw6pzPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgPE1vZHVsZUJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dVcGdyYWRlTW9kYWwoZmFsc2UpfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17YWN0aW9uTG9hZGluZ31cbiAgICAgICAgICAgICAgbW9kdWxlQ29sb3I9XCJhZG1pblwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIENhbmNlbGFyXG4gICAgICAgICAgICA8L01vZHVsZUJ1dHRvbj5cbiAgICAgICAgICAgIDxNb2R1bGVCdXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVXBncmFkZVBsYW4oJ3Byb2Zlc3Npb25hbCcpfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17YWN0aW9uTG9hZGluZ31cbiAgICAgICAgICAgICAgbW9kdWxlQ29sb3I9XCJhZG1pblwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHthY3Rpb25Mb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cImgtNCB3LTQgYW5pbWF0ZS1zcGluIG1yLTJcIiAvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxBcnJvd1VwQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIEVzY29saGVyIFByb2Zpc3Npb25hbFxuICAgICAgICAgICAgPC9Nb2R1bGVCdXR0b24+XG4gICAgICAgICAgICA8TW9kdWxlQnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVVwZ3JhZGVQbGFuKCdlbnRlcnByaXNlJyl9XG4gICAgICAgICAgICAgIGRpc2FibGVkPXthY3Rpb25Mb2FkaW5nfVxuICAgICAgICAgICAgICBtb2R1bGVDb2xvcj1cImFkbWluXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWwtMlwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHthY3Rpb25Mb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cImgtNCB3LTQgYW5pbWF0ZS1zcGluIG1yLTJcIiAvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxBcnJvd1VwQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIEVzY29saGVyIEVudGVycHJpc2VcbiAgICAgICAgICAgIDwvTW9kdWxlQnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvTW9kdWxlTW9kYWw+XG5cbiAgICAgIHsvKiBNb2RhbCBkZSBDb21wcmEgZGUgVXN1w6FyaW9zICovfVxuICAgICAgPE1vZHVsZU1vZGFsXG4gICAgICAgIGlzT3Blbj17c2hvd1VzZXJNb2RhbH1cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd1VzZXJNb2RhbChmYWxzZSl9XG4gICAgICAgIHRpdGxlPVwiQ29tcHJhciBNYWlzIFVzdcOhcmlvc1wiXG4gICAgICAgIG1vZHVsZUNvbG9yPVwiYWRtaW5cIlxuICAgICAgPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byB3LTE2IGgtMTYgYmctYmx1ZS0xMDAgZGFyazpiZy1ibHVlLTkwMC8yMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgICAgICA8VXNlcnMgY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LWJsdWUtNjAwIGRhcms6dGV4dC1ibHVlLTQwMFwiIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGUgbWItMlwiPlxuICAgICAgICAgICAgICBBZGljaW9uYXIgVXN1w6FyaW9zXG4gICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgRXhwYW5kYSBzdWEgZXF1aXBlIGNvbSB1c3XDoXJpb3MgYWRpY2lvbmFpc1xuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIGRhcms6YmctZ3JheS04MDAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIG1iLTRcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlVzdcOhcmlvcyBhdHVhaXM6PC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+e3VzZXJTdGF0cy5jdXJyZW50fTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPkxpbWl0ZSBhdHVhbDo8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj57dXNlclN0YXRzLmxpbWl0fTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZSBtYi00XCI+UGFjb3RlcyBSw6FwaWRvczo8L2g0PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIHtbXG4gICAgICAgICAgICAgICAgICB7IHVzZXJzOiAxMCwgcG9wdWxhcjogZmFsc2UgfSxcbiAgICAgICAgICAgICAgICAgIHsgdXNlcnM6IDI1LCBwb3B1bGFyOiB0cnVlIH0sXG4gICAgICAgICAgICAgICAgICB7IHVzZXJzOiA1MCwgcG9wdWxhcjogZmFsc2UgfVxuICAgICAgICAgICAgICAgIF0ubWFwKChwYWNrKSA9PiB7XG4gICAgICAgICAgICAgICAgICAvLyBDYWxjdWxhciBwcmXDp28gZGluYW1pY2FtZW50ZVxuICAgICAgICAgICAgICAgICAgY29uc3QgYmFzZVByaWNlID0gMTkuOTA7XG4gICAgICAgICAgICAgICAgICBjb25zdCBnZXREaXNjb3VudEJ5VXNlckNvdW50ID0gKHVzZXJzKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlmICh1c2VycyA+PSAyMDApIHJldHVybiA0MDtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHVzZXJzID49IDEwMCkgcmV0dXJuIDM1O1xuICAgICAgICAgICAgICAgICAgICBpZiAodXNlcnMgPj0gNTApIHJldHVybiAyNTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHVzZXJzID49IDIwKSByZXR1cm4gMTU7XG4gICAgICAgICAgICAgICAgICAgIGlmICh1c2VycyA+PSA1KSByZXR1cm4gMTA7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAwO1xuICAgICAgICAgICAgICAgICAgfTtcblxuICAgICAgICAgICAgICAgICAgY29uc3QgZGlzY291bnQgPSBnZXREaXNjb3VudEJ5VXNlckNvdW50KHBhY2sudXNlcnMpO1xuICAgICAgICAgICAgICAgICAgY29uc3QgdG90YWxXaXRob3V0RGlzY291bnQgPSBwYWNrLnVzZXJzICogYmFzZVByaWNlO1xuICAgICAgICAgICAgICAgICAgY29uc3QgZGlzY291bnRBbW91bnQgPSB0b3RhbFdpdGhvdXREaXNjb3VudCAqIChkaXNjb3VudCAvIDEwMCk7XG4gICAgICAgICAgICAgICAgICBjb25zdCBmaW5hbFByaWNlID0gdG90YWxXaXRob3V0RGlzY291bnQgLSBkaXNjb3VudEFtb3VudDtcblxuICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgIGtleT17cGFjay51c2Vyc31cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2ByZWxhdGl2ZSBwLTQgYm9yZGVyLTIgcm91bmRlZC1sZyBjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWFsbCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgcGFjay5wb3B1bGFyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1ibHVlLTUwMCBiZy1ibHVlLTUwIGRhcms6YmctYmx1ZS05MDAvMjAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMCBob3Zlcjpib3JkZXItYmx1ZS0zMDAnXG4gICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQnV5TW9yZVVzZXJzKHBhY2sudXNlcnMpfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge3BhY2sucG9wdWxhciAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtMiBsZWZ0LTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS14LTEvMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJiZy1ibHVlLTUwMCB0ZXh0LXdoaXRlIHRleHQteHMgcHgtMiBweS0xIHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIE1haXMgUG9wdWxhclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZSBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICt7cGFjay51c2Vyc31cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgbWItM1wiPnVzdcOhcmlvczwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWJsdWUtNjAwIGRhcms6dGV4dC1ibHVlLTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0Q3VycmVuY3koZmluYWxQcmljZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+cG9yIG3DqnM8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICB7ZGlzY291bnQgPiAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyZWVuLTYwMCBkYXJrOnRleHQtZ3JlZW4tNDAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZGlzY291bnR9JSBkZSBkZXNjb250b1xuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwIGRhcms6Ym9yZGVyLWdyYXktNzAwIHB0LTZcIj5cbiAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlIG1iLTRcIj5RdWFudGlkYWRlIFBlcnNvbmFsaXphZGE6PC9oND5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCBtYi00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEN1c3RvbVVzZXJDb3VudChNYXRoLm1heCgxLCBjdXN0b21Vc2VyQ291bnQgLSAxKSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctOCBoLTggcm91bmRlZC1mdWxsIGJnLWdyYXktMjAwIGRhcms6YmctZ3JheS02MDAgaG92ZXI6YmctZ3JheS0zMDAgZGFyazpob3ZlcjpiZy1ncmF5LTUwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIC1cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICBtaW49XCIxXCJcbiAgICAgICAgICAgICAgICAgICAgbWF4PVwiNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2N1c3RvbVVzZXJDb3VudH1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDdXN0b21Vc2VyQ291bnQoTWF0aC5tYXgoMSwgcGFyc2VJbnQoZS50YXJnZXQudmFsdWUpIHx8IDEpKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0yMCB0ZXh0LWNlbnRlciB0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGUgYmctZ3JheS0xMDAgZGFyazpiZy1ncmF5LTcwMCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRDdXN0b21Vc2VyQ291bnQoTWF0aC5taW4oNTAwLCBjdXN0b21Vc2VyQ291bnQgKyAxKSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctOCBoLTggcm91bmRlZC1mdWxsIGJnLWdyYXktMjAwIGRhcms6YmctZ3JheS02MDAgaG92ZXI6YmctZ3JheS0zMDAgZGFyazpob3ZlcjpiZy1ncmF5LTUwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICtcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICBQcmXDp28gZXN0aW1hZG86IDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7KCgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGJhc2VQcmljZSA9IDE5LjkwO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZ2V0RGlzY291bnRCeVVzZXJDb3VudCA9ICh1c2VycykgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodXNlcnMgPj0gMjAwKSByZXR1cm4gNDA7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlmICh1c2VycyA+PSAxMDApIHJldHVybiAzNTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHVzZXJzID49IDUwKSByZXR1cm4gMjU7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlmICh1c2VycyA+PSAyMCkgcmV0dXJuIDE1O1xuICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodXNlcnMgPj0gNSkgcmV0dXJuIDEwO1xuICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gMDtcbiAgICAgICAgICAgICAgICAgICAgICAgIH07XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGRpc2NvdW50ID0gZ2V0RGlzY291bnRCeVVzZXJDb3VudChjdXN0b21Vc2VyQ291bnQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdG90YWxXaXRob3V0RGlzY291bnQgPSBjdXN0b21Vc2VyQ291bnQgKiBiYXNlUHJpY2U7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBkaXNjb3VudEFtb3VudCA9IHRvdGFsV2l0aG91dERpc2NvdW50ICogKGRpc2NvdW50IC8gMTAwKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpbmFsUHJpY2UgPSB0b3RhbFdpdGhvdXREaXNjb3VudCAtIGRpc2NvdW50QW1vdW50O1xuXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZm9ybWF0Q3VycmVuY3koZmluYWxQcmljZSk7XG4gICAgICAgICAgICAgICAgICAgICAgfSkoKX1cbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPi9tw6pzXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPE1vZHVsZUJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUJ1eU1vcmVVc2VycyhjdXN0b21Vc2VyQ291bnQpfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXthY3Rpb25Mb2FkaW5nfVxuICAgICAgICAgICAgICAgIG1vZHVsZUNvbG9yPVwiYWRtaW5cIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7YWN0aW9uTG9hZGluZyA/IChcbiAgICAgICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cImgtNCB3LTQgYW5pbWF0ZS1zcGluIG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgQ29tcHJhciB7Y3VzdG9tVXNlckNvdW50fSBVc3XDoXJpb3NcbiAgICAgICAgICAgICAgPC9Nb2R1bGVCdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgIDxNb2R1bGVCdXR0b25cbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93VXNlck1vZGFsKGZhbHNlKX1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2FjdGlvbkxvYWRpbmd9XG4gICAgICAgICAgICAgIG1vZHVsZUNvbG9yPVwiYWRtaW5cIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBGZWNoYXJcbiAgICAgICAgICAgIDwvTW9kdWxlQnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvTW9kdWxlTW9kYWw+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBQbGFuc1BhZ2U7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkNyZWRpdENhcmQiLCJDYWxlbmRhciIsIlVzZXJzIiwiQ2hlY2tDaXJjbGUiLCJYQ2lyY2xlIiwiQWxlcnRDaXJjbGUiLCJQbHVzIiwiTWludXMiLCJEb3dubG9hZCIsIlJlZnJlc2hDdyIsIkxvYWRlcjIiLCJDbG9jayIsIkRvbGxhclNpZ24iLCJQYWNrYWdlIiwiU2V0dGluZ3MiLCJUcmFzaDIiLCJUcmVuZGluZ1VwIiwiU2hpZWxkIiwiWmFwIiwiQ3Jvd24iLCJVc2VyUGx1cyIsIkFycm93VXBDaXJjbGUiLCJSb3RhdGVDY3ciLCJGaWxlVGV4dCIsIkFjdGl2aXR5IiwiQmFyQ2hhcnQzIiwiV2FsbGV0IiwiSGlzdG9yeSIsIkV5ZSIsInN1YnNjcmlwdGlvblNlcnZpY2UiLCJ1c2VBdXRoIiwidXNlVG9hc3QiLCJhcGkiLCJNb2R1bGVIZWFkZXIiLCJNb2R1bGVCdXR0b24iLCJNb2R1bGVNb2RhbCIsIk1vZHVsZVNlbGVjdCIsIk1vZHVsZUNhcmQiLCJQbGFuc1BhZ2UiLCJzdWJzY3JpcHRpb24iLCJ1c2VyIiwiYWRkVG9hc3QiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJzZXRTdWJzY3JpcHRpb24iLCJwbGFucyIsInNldFBsYW5zIiwiaW52b2ljZXMiLCJzZXRJbnZvaWNlcyIsInNob3dDYW5jZWxNb2RhbCIsInNldFNob3dDYW5jZWxNb2RhbCIsInNob3dNb2R1bGVNb2RhbCIsInNldFNob3dNb2R1bGVNb2RhbCIsInNob3dVcGdyYWRlTW9kYWwiLCJzZXRTaG93VXBncmFkZU1vZGFsIiwic2hvd1VzZXJNb2RhbCIsInNldFNob3dVc2VyTW9kYWwiLCJzZWxlY3RlZE1vZHVsZSIsInNldFNlbGVjdGVkTW9kdWxlIiwiYWN0aW9uTG9hZGluZyIsInNldEFjdGlvbkxvYWRpbmciLCJjb21wYW5pZXMiLCJzZXRDb21wYW5pZXMiLCJzZWxlY3RlZENvbXBhbnlJZCIsInNldFNlbGVjdGVkQ29tcGFueUlkIiwidXNlclN0YXRzIiwic2V0VXNlclN0YXRzIiwiY3VycmVudCIsImxpbWl0IiwiY3VzdG9tVXNlckNvdW50Iiwic2V0Q3VzdG9tVXNlckNvdW50Iiwicm9sZSIsImxvYWRDb21wYW5pZXMiLCJsb2FkRGF0YSIsInJlc3BvbnNlIiwiZ2V0IiwiZGF0YSIsImVycm9yIiwiY29uc29sZSIsInF1ZXJ5UGFyYW1zIiwic3Vic2NyaXB0aW9uRGF0YSIsImludm9pY2VzRGF0YSIsInBsYW5zRGF0YSIsInVzZXJTdGF0c0RhdGEiLCJ0aGVuIiwicmVzIiwic3RhdHVzIiwid2FybiIsImdldFBsYW5zIiwidXNlcnNSZXNwb25zZSIsImFjdGl2ZVVzZXJzIiwidXNlcnMiLCJmaWx0ZXIiLCJ1IiwiYWN0aXZlIiwibGVuZ3RoIiwidXNlckxpbWl0IiwiaGFuZGxlQWRkTW9kdWxlIiwibW9kdWxlVHlwZSIsImFkZE1vZHVsZSIsIm1lc3NhZ2UiLCJoYW5kbGVSZW1vdmVNb2R1bGUiLCJyZW1vdmVNb2R1bGUiLCJoYW5kbGVDYW5jZWxTdWJzY3JpcHRpb24iLCJjYW5jZWxTdWJzY3JpcHRpb24iLCJoYW5kbGVVcGdyYWRlUGxhbiIsInBsYW5UeXBlIiwiZmluYWxVc2VyTGltaXQiLCJ1cGdyYWRlUGxhbiIsImhhbmRsZUJ1eU1vcmVVc2VycyIsImFkZGl0aW9uYWxVc2VycyIsImFkZFVzZXJzIiwiZ2V0U3RhdHVzQ29sb3IiLCJnZXRTdGF0dXNUZXh0IiwiZm9ybWF0RGF0ZSIsImRhdGVTdHJpbmciLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwiZm9ybWF0Q3VycmVuY3kiLCJ2YWx1ZSIsIkludGwiLCJOdW1iZXJGb3JtYXQiLCJzdHlsZSIsImN1cnJlbmN5IiwiZm9ybWF0IiwiZGl2IiwiY2xhc3NOYW1lIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImljb24iLCJzaXplIiwibW9kdWxlQ29sb3IiLCJoMyIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsIm9wdGlvbiIsIm1hcCIsImNvbXBhbnkiLCJpZCIsIm5hbWUiLCJwIiwiaDQiLCJiaWxsaW5nQ3ljbGUiLCJzcGFuIiwicHJpY2VQZXJNb250aCIsIm5leHRCaWxsaW5nRGF0ZSIsInN0YXJ0RGF0ZSIsImNhbmNlbEF0UGVyaW9kRW5kIiwid2lkdGgiLCJNYXRoIiwibWluIiwibW9kdWxlcyIsIm0iLCJ2YXJpYW50Iiwib25DbGljayIsIm1vZHVsZSIsImluY2x1ZGVzIiwiZGlzYWJsZWQiLCJ0YWJsZSIsInRoZWFkIiwidHIiLCJ0aCIsInRib2R5IiwiaW52b2ljZSIsInRkIiwiY3JlYXRlZEF0IiwiYW1vdW50IiwiZHVlRGF0ZSIsInN0cmlwZUludm9pY2VVcmwiLCJhIiwiaHJlZiIsInJlbCIsImlzT3BlbiIsIm9uQ2xvc2UiLCJPYmplY3QiLCJlbnRyaWVzIiwibW9kdWxlSW5mbyIsImlzQWN0aXZlIiwiZmluZCIsImlzQmFzaWMiLCJtb250aGx5UHJpY2UiLCJ1bCIsImxpIiwicG9wdWxhciIsInBhY2siLCJiYXNlUHJpY2UiLCJnZXREaXNjb3VudEJ5VXNlckNvdW50IiwiZGlzY291bnQiLCJ0b3RhbFdpdGhvdXREaXNjb3VudCIsImRpc2NvdW50QW1vdW50IiwiZmluYWxQcmljZSIsImJ1dHRvbiIsInR5cGUiLCJtYXgiLCJpbnB1dCIsInBhcnNlSW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js\n"));

/***/ })

});